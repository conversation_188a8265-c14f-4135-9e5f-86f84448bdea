==========================
Biometric Attendance System
==========================

.. image:: static/description/banner.png
   :alt: Biometric Attendance System
   :width: 100%

A comprehensive biometric attendance management system for Odoo 15 that provides complete independence from standard HR modules.

Features
========

🔐 **Device Integration**
------------------------
* Direct TCP/IP connection to ZKTeco biometric devices (SpeedFace-V5L, etc.)
* Real-time data synchronization
* Support for fingerprint, face recognition, and card verification
* Automatic device connection monitoring

👥 **Employee Management**
-------------------------
* Independent employee records (no dependency on hr.employee)
* Biometric ID and card number management
* Employee photo and biometric template storage
* Department and position tracking

⏰ **Attendance Tracking**
-------------------------
* Automatic check-in/check-out recording
* Manual attendance entry support
* Real-time status calculation (Present, Late, Absent, Early Leave)
* Working hours and overtime calculation

📅 **Work Schedules**
--------------------
* Flexible work schedule configuration
* Multiple shift support
* Break time management
* Holiday calendar integration
* Grace period settings

📊 **Comprehensive Reports**
---------------------------
* Daily, weekly, and monthly attendance reports
* Employee attendance summaries
* Excel and PDF export capabilities
* Customizable report filters

🔧 **System Administration**
----------------------------
* Multi-level user permissions (User, Supervisor, Administrator)
* Automated data processing
* Scheduled synchronization tasks
* System settings and configuration

Installation
============

1. **Download the Module**
   
   Download or clone this module to your Odoo addons directory::

       git clone <repository-url> /path/to/odoo/addons/biometric_attendance

2. **Install Dependencies**
   
   Ensure you have the required Python packages::

       pip install socket struct datetime

3. **Update Apps List**
   
   In Odoo, go to Apps → Update Apps List

4. **Install the Module**
   
   Search for "Biometric Attendance Management" and click Install

Configuration
=============

Initial Setup
-------------

1. **Create Work Schedules**
   
   * Go to Biometric Attendance → Configuration → Work Schedules
   * Create schedules for different shifts or departments
   * Define working hours, break times, and grace periods

2. **Add Biometric Devices**
   
   * Go to Biometric Attendance → Devices → Biometric Devices
   * Add your ZKTeco devices with IP address and port
   * Test connection to ensure proper communication

3. **Configure Employees**
   
   * Go to Biometric Attendance → Employees → Employees
   * Add employees with biometric IDs matching device user IDs
   * Assign work schedules to employees

4. **Set Up Holidays**
   
   * Go to Biometric Attendance → Configuration → Holidays
   * Define public holidays and non-working days

Device Configuration
-------------------

**ZKTeco Device Setup:**

1. Ensure device is connected to network
2. Configure device IP address (static recommended)
3. Enable TCP/IP communication on device
4. Note device serial number for webhook configuration

**Supported Devices:**
* ZKTeco SpeedFace-V5L
* ZKTeco K40
* ZKTeco F18
* Other ZKTeco TCP/IP enabled devices

Usage
=====

Daily Operations
---------------

**Automatic Sync:**
* Attendance data syncs automatically every hour
* Employee data syncs weekly
* Device connections tested every 30 minutes

**Manual Operations:**
* Sync devices manually via Device → Sync Device
* Process attendance via Configuration → Process Attendance
* Generate reports via Reports → Attendance Report

**Employee Dashboard:**
* View personal attendance status
* Manual check-in/check-out (if enabled)
* Monthly attendance summary

Permissions
===========

The module includes three permission levels:

**Biometric User (View Only)**
* View attendance records
* View employee information
* Access personal dashboard

**Attendance Supervisor**
* All User permissions
* Create/edit attendance records
* Create/edit employees
* Generate reports
* Manage schedules and holidays

**Biometric Administrator**
* All Supervisor permissions
* Manage devices
* System configuration
* Delete records
* Access all administrative functions

API Endpoints
=============

The module provides REST API endpoints for integration:

**Manual Check-in:**
::

    POST /biometric/attendance/api/checkin
    {
        "employee_id": 123
    }

**Manual Check-out:**
::

    POST /biometric/attendance/api/checkout
    {
        "employee_id": 123
    }

**Attendance Status:**
::

    GET /biometric/attendance/api/status?employee_id=123

**Device Webhook:**
::

    POST /biometric/device/webhook
    {
        "device_id": "device_serial",
        "user_id": "employee_biometric_id",
        "timestamp": "2023-12-01 09:00:00",
        "type": "check_in",
        "verification_type": "fingerprint"
    }

Scheduled Tasks
===============

The module includes several automated tasks:

* **Auto Sync Attendance** (Hourly): Syncs attendance from all devices
* **Process Daily Attendance** (Daily): Processes and calculates attendance status
* **Auto Sync Employees** (Weekly): Syncs employee data from devices
* **Test Device Connections** (Every 30 minutes): Monitors device connectivity
* **Clean Old Records** (Monthly): Archives old attendance data (optional)

Troubleshooting
===============

Common Issues
-------------

**Device Connection Failed:**
* Check network connectivity
* Verify IP address and port
* Ensure device TCP/IP is enabled
* Check firewall settings

**Employee Not Found:**
* Verify biometric ID matches device user ID
* Check employee is active
* Ensure employee has assigned schedule

**Attendance Not Syncing:**
* Check device connection status
* Verify scheduled tasks are running
* Check system logs for errors

**Reports Not Generating:**
* Ensure date range is valid
* Check employee has attendance data
* Verify user permissions

Support
=======

For technical support and customization:

* Email: <EMAIL>
* Documentation: https://docs.yourcompany.com/biometric-attendance
* Issues: https://github.com/yourcompany/biometric-attendance/issues

License
=======

This module is licensed under LGPL-3.

Copyright (c) 2023 Your Company Name

Changelog
=========

Version ********.0
------------------
* Initial release
* ZKTeco device integration
* Complete attendance management
* Multi-level permissions
* Comprehensive reporting
* API endpoints
* Automated synchronization

Credits
=======

**Contributors:**
* Your Name <<EMAIL>>

**Special Thanks:**
* ZKTeco for device documentation
* Odoo Community for framework support
