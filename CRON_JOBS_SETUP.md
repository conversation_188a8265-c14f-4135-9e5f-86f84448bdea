# إعداد المهام المجدولة لموديول Biometric Attendance

## 📋 نظرة عامة

تم إنشاء المهام المجدولة في حالة غير نشطة لتجنب مشاكل التثبيت. يمكنك تفعيلها وتخصيصها بعد التثبيت.

## 🔧 تفعيل المهام المجدولة

### 1. الوصول إلى المهام المجدولة
```
Settings → Technical → Automation → Scheduled Actions
```

### 2. المهام المتاحة

#### أ) Auto Sync Biometric Attendance
- **الوصف**: مزامنة تلقائية لبيانات الحضور من الأجهزة
- **التكرار**: كل ساعة
- **الحالة**: غير نشط (يجب تفعيله يدوياً)

**الكود المقترح:**
```python
devices = model.search([('active', '=', True), ('device_type', '=', 'zkteco')])
for device in devices:
    try:
        if device.test_connection():
            device.sync_attendance()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error("Auto sync failed for device %s: %s" % (device.name, str(e)))
```

#### ب) Process Daily Attendance
- **الوصف**: معالجة يومية لبيانات الحضور وحساب التأخير والغياب
- **التكرار**: يومياً في الساعة 1:00 صباحاً
- **الحالة**: غير نشط

**الكود المقترح:**
```python
from datetime import date, timedelta
yesterday = date.today() - timedelta(days=1)
model.process_daily_attendance(yesterday)
```

#### ج) Test Biometric Device Connections
- **الوصف**: اختبار الاتصال بالأجهزة البيومترية
- **التكرار**: كل 30 دقيقة
- **الحالة**: غير نشط

**الكود المقترح:**
```python
devices = model.search([('active', '=', True)])
for device in devices:
    try:
        device.test_connection()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.warning("Connection test failed for device %s: %s" % (device.name, str(e)))
```

## ⚙️ خطوات التفعيل

### 1. تفعيل مهمة المزامنة التلقائية
1. اذهب إلى `Settings → Technical → Automation → Scheduled Actions`
2. ابحث عن "Auto Sync Biometric Attendance"
3. افتح السجل
4. استبدل الكود في حقل "Python Code" بالكود المقترح أعلاه
5. فعّل المهمة بتحديد "Active"
6. احفظ التغييرات

### 2. تفعيل معالجة الحضور اليومية
1. ابحث عن "Process Daily Attendance"
2. استبدل الكود بالكود المقترح
3. فعّل المهمة
4. احفظ التغييرات

### 3. تفعيل اختبار الاتصال
1. ابحث عن "Test Biometric Device Connections"
2. استبدل الكود بالكود المقترح
3. فعّل المهمة (اختياري)
4. احفظ التغييرات

## 🔍 مراقبة المهام

### عرض سجلات المهام
```
Settings → Technical → Automation → Scheduled Actions
```
- اضغط على المهمة المطلوبة
- اذهب إلى تبويب "Logs" لعرض سجل التنفيذ

### فحص الأخطاء
```
Settings → Technical → Logging
```
- ابحث عن رسائل الخطأ المتعلقة بـ "biometric"

## ⚠️ تحذيرات مهمة

1. **اختبر المهام أولاً**: قم بتشغيل المهام يدوياً قبل تفعيلها تلقائياً
2. **راقب الأداء**: تأكد من أن المهام لا تؤثر على أداء النظام
3. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية قبل تفعيل المهام
4. **الاختبار التدريجي**: فعّل مهمة واحدة في كل مرة

## 🛠️ تخصيص إضافي

### تغيير أوقات التنفيذ
- يمكنك تعديل `Interval Number` و `Interval Type` حسب احتياجاتك
- مثال: كل 30 دقيقة، كل 2 ساعة، كل يوم، إلخ

### إضافة مهام جديدة
يمكنك إنشاء مهام مجدولة إضافية مثل:
- تنظيف البيانات القديمة
- إرسال تقارير دورية
- نسخ احتياطي للبيانات

## 📞 الدعم

إذا واجهت مشاكل في إعداد المهام المجدولة:
1. تحقق من سجلات النظام
2. تأكد من صحة الكود المستخدم
3. اختبر الاتصال بالأجهزة يدوياً
4. راجع صلاحيات المستخدم

---

**ملاحظة**: تم تعطيل المهام افتراضياً لضمان التثبيت السلس للموديول. يمكنك تفعيلها عند الحاجة.
