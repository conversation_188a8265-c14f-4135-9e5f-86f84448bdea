# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, date, timedelta
import base64
import io
import xlsxwriter


class BiometricAttendanceReportWizard(models.TransientModel):
    _name = 'biometric.attendance.report.wizard'
    _description = 'Attendance Report Wizard'

    # Report Parameters
    report_type = fields.Selection([
        ('daily', 'Daily Report'),
        ('weekly', 'Weekly Report'),
        ('monthly', 'Monthly Report'),
        ('custom', 'Custom Period'),
    ], string='Report Type', required=True, default='monthly')
    
    date_from = fields.Date(string='Date From', required=True, default=fields.Date.today)
    date_to = fields.Date(string='Date To', required=True, default=fields.Date.today)
    
    # Filters
    employee_ids = fields.Many2many('biometric.employee', string='Employees')
    department = fields.Char(string='Department')
    schedule_ids = fields.Many2many('biometric.schedule', string='Schedules')
    
    # Report Options
    include_absent = fields.Boolean(string='Include Absent Days', default=True)
    include_holidays = fields.Boolean(string='Include Holidays', default=False)
    group_by_employee = fields.Boolean(string='Group by Employee', default=True)
    show_details = fields.Boolean(string='Show Details', default=True)
    
    # Output Format
    output_format = fields.Selection([
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
    ], string='Output Format', required=True, default='pdf')
    
    @api.onchange('report_type')
    def _onchange_report_type(self):
        today = date.today()
        if self.report_type == 'daily':
            self.date_from = today
            self.date_to = today
        elif self.report_type == 'weekly':
            # Start of current week (Monday)
            start_of_week = today - timedelta(days=today.weekday())
            self.date_from = start_of_week
            self.date_to = start_of_week + timedelta(days=6)
        elif self.report_type == 'monthly':
            # Start of current month
            self.date_from = today.replace(day=1)
            # End of current month
            next_month = today.replace(day=28) + timedelta(days=4)
            self.date_to = next_month - timedelta(days=next_month.day)
    
    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for wizard in self:
            if wizard.date_from > wizard.date_to:
                raise ValidationError(_('Date From must be before Date To!'))
    
    def action_generate_report(self):
        """Generate the attendance report"""
        self.ensure_one()
        
        if self.output_format == 'pdf':
            return self._generate_pdf_report()
        else:
            return self._generate_excel_report()
    
    def _generate_pdf_report(self):
        """Generate PDF report"""
        data = self._prepare_report_data()
        
        return self.env.ref('biometric_attendance.action_attendance_report').report_action(
            self, data=data
        )
    
    def _generate_excel_report(self):
        """Generate Excel report"""
        data = self._prepare_report_data()
        
        # Create Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        
        # Create worksheet
        worksheet = workbook.add_worksheet('Attendance Report')
        
        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D7E4BC',
            'border': 1,
            'align': 'center'
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'align': 'center'
        })
        
        date_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'num_format': 'dd/mm/yyyy'
        })
        
        time_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'num_format': 'hh:mm'
        })
        
        # Write headers
        headers = [
            'Employee Code', 'Employee Name', 'Date', 'Check In', 'Check Out',
            'Status', 'Late Minutes', 'Worked Hours', 'Overtime Hours'
        ]
        
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # Write data
        row = 1
        for employee_data in data['employees']:
            for record in employee_data['records']:
                worksheet.write(row, 0, employee_data['employee'].employee_code, cell_format)
                worksheet.write(row, 1, employee_data['employee'].name, cell_format)
                worksheet.write(row, 2, record.get('date'), date_format)
                worksheet.write(row, 3, record.get('check_in'), time_format)
                worksheet.write(row, 4, record.get('check_out'), time_format)
                worksheet.write(row, 5, record.get('status', ''), cell_format)
                worksheet.write(row, 6, record.get('late_minutes', 0), cell_format)
                worksheet.write(row, 7, record.get('worked_hours', 0), cell_format)
                worksheet.write(row, 8, record.get('overtime_hours', 0), cell_format)
                row += 1
        
        # Auto-adjust column widths
        for col in range(len(headers)):
            worksheet.set_column(col, col, 15)
        
        workbook.close()
        output.seek(0)
        
        # Create attachment
        filename = f"attendance_report_{self.date_from}_{self.date_to}.xlsx"
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(output.read()),
            'res_model': self._name,
            'res_id': self.id,
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'new',
        }
    
    def _prepare_report_data(self):
        """Prepare data for the report"""
        # Get employees
        employees = self.employee_ids
        if not employees:
            domain = [('active', '=', True)]
            if self.department:
                domain.append(('department', 'ilike', self.department))
            if self.schedule_ids:
                domain.append(('schedule_id', 'in', self.schedule_ids.ids))
            employees = self.env['biometric.employee'].search(domain)
        
        # Prepare report data
        report_data = {
            'wizard': self,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'employees': [],
            'summary': {
                'total_employees': len(employees),
                'total_present': 0,
                'total_late': 0,
                'total_absent': 0,
                'total_working_hours': 0,
                'total_overtime_hours': 0,
            }
        }
        
        processor = self.env['biometric.attendance.processor']
        
        for employee in employees:
            # Get attendance summary
            summary = processor.generate_attendance_summary(
                employee.id, self.date_from, self.date_to
            )
            
            # Get detailed records
            records = []
            current_date = self.date_from
            
            while current_date <= self.date_to:
                # Check if it's a working day
                if processor._is_working_day(employee, current_date):
                    # Get attendance for this date
                    attendances = self.env['biometric.attendance'].search([
                        ('employee_id', '=', employee.id),
                        ('attendance_date', '=', current_date)
                    ])
                    
                    if attendances:
                        check_ins = attendances.filtered(lambda a: a.attendance_type == 'check_in')
                        check_outs = attendances.filtered(lambda a: a.attendance_type == 'check_out')
                        
                        first_in = check_ins[0] if check_ins else None
                        last_out = check_outs[-1] if check_outs else None
                        
                        record = {
                            'date': current_date,
                            'check_in': first_in.check_time.time() if first_in else None,
                            'check_out': last_out.check_time.time() if last_out else None,
                            'status': first_in.status if first_in else 'absent',
                            'late_minutes': first_in.late_minutes if first_in else 0,
                            'worked_hours': first_in.worked_hours if first_in else 0,
                            'overtime_hours': first_in.overtime_hours if first_in else 0,
                        }
                    else:
                        # Absent
                        record = {
                            'date': current_date,
                            'check_in': None,
                            'check_out': None,
                            'status': 'absent',
                            'late_minutes': 0,
                            'worked_hours': 0,
                            'overtime_hours': 0,
                        }
                    
                    if self.include_absent or record['status'] != 'absent':
                        records.append(record)
                
                current_date += timedelta(days=1)
            
            employee_data = {
                'employee': employee,
                'summary': summary,
                'records': records,
            }
            
            report_data['employees'].append(employee_data)
            
            # Update totals
            if summary:
                report_data['summary']['total_present'] += summary['status_counts'].get('present', 0)
                report_data['summary']['total_late'] += summary['status_counts'].get('late', 0)
                report_data['summary']['total_absent'] += summary['status_counts'].get('absent', 0)
                report_data['summary']['total_working_hours'] += summary['hours_data'].get('total_hours', 0)
                report_data['summary']['total_overtime_hours'] += summary['hours_data'].get('total_overtime', 0)
        
        return report_data


class BiometricEmployeeSummaryWizard(models.TransientModel):
    _name = 'biometric.employee.summary.wizard'
    _description = 'Employee Summary Wizard'

    employee_id = fields.Many2one('biometric.employee', string='Employee', required=True)
    date_from = fields.Date(string='Date From', required=True, default=lambda self: date.today().replace(day=1))
    date_to = fields.Date(string='Date To', required=True, default=fields.Date.today)
    
    def action_view_summary(self):
        """View employee attendance summary"""
        self.ensure_one()
        
        processor = self.env['biometric.attendance.processor']
        summary = processor.generate_attendance_summary(
            self.employee_id.id, self.date_from, self.date_to
        )
        
        return {
            'name': _('Employee Attendance Summary'),
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.attendance',
            'view_mode': 'tree,form',
            'domain': [
                ('employee_id', '=', self.employee_id.id),
                ('attendance_date', '>=', self.date_from),
                ('attendance_date', '<=', self.date_to)
            ],
            'context': {
                'search_default_group_date': 1,
                'summary_data': summary,
            },
        }
