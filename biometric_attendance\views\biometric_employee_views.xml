<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Employee Tree View -->
    <record id="view_biometric_employee_tree" model="ir.ui.view">
        <field name="name">biometric.employee.tree</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <tree string="Employees" decoration-muted="not active">
                <field name="employee_code"/>
                <field name="name"/>
                <field name="biometric_id"/>
                <field name="department"/>
                <field name="position"/>
                <field name="schedule_id"/>
                <field name="total_attendances"/>
                <field name="total_delays"/>
                <field name="is_synced" widget="boolean_toggle"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Employee Form View -->
    <record id="view_biometric_employee_form" model="ir.ui.view">
        <field name="name">biometric.employee.form</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <form string="Employee">
                <header>
                    <button name="action_sync_with_device" type="object" string="Sync with Device" 
                            class="btn-primary" attrs="{'invisible': [('is_synced', '=', True)]}"/>
                    <button name="action_view_attendances" type="object" string="View Attendances" 
                            class="btn-secondary"/>
                    <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_attendances" type="object" class="oe_stat_button" icon="fa-calendar">
                            <field name="total_attendances" widget="statinfo" string="Attendances"/>
                        </button>
                        <button name="action_view_attendances" type="object" class="oe_stat_button" icon="fa-clock-o">
                            <field name="total_delays" widget="statinfo" string="Delays"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Synced" bg_color="bg-success" 
                            attrs="{'invisible': [('is_synced', '=', False)]}"/>
                    
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="name" required="1"/>
                            <field name="employee_code" required="1"/>
                            <field name="biometric_id" required="1"/>
                            <field name="card_number"/>
                            <field name="job_number"/>
                        </group>
                        <group name="employment_info" string="Employment Information">
                            <field name="department"/>
                            <field name="position"/>
                            <field name="hire_date"/>
                            <field name="schedule_id" required="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="contact_info" string="Contact Information">
                            <field name="email" widget="email"/>
                            <field name="phone" widget="phone"/>
                            <field name="mobile" widget="phone"/>
                        </group>
                        <group name="sync_info" string="Sync Information">
                            <field name="is_synced" readonly="1"/>
                            <field name="last_sync_date" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Biometric Data" name="biometric_data">
                            <group>
                                <field name="fingerprint_template" readonly="1"/>
                                <field name="face_template" readonly="1"/>
                            </group>
                        </page>
                        <page string="Attendance Records" name="attendance_records">
                            <field name="attendance_ids" readonly="1">
                                <tree>
                                    <field name="attendance_date"/>
                                    <field name="check_time"/>
                                    <field name="attendance_type"/>
                                    <field name="status" decoration-success="status == 'present'" 
                                           decoration-warning="status == 'late'" 
                                           decoration-danger="status == 'absent'"/>
                                    <field name="worked_hours" widget="float_time"/>
                                    <field name="late_minutes"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Employee Kanban View -->
    <record id="view_biometric_employee_kanban" model="ir.ui.view">
        <field name="name">biometric.employee.kanban</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="id"/>
                <field name="name"/>
                <field name="employee_code"/>
                <field name="department"/>
                <field name="position"/>
                <field name="total_attendances"/>
                <field name="total_delays"/>
                <field name="is_synced"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click #{record.active.raw_value ? '' : 'oe_kanban_color_2'}">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <small class="o_kanban_record_subtitle text-muted">
                                        [<field name="employee_code"/>]
                                    </small>
                                </div>
                                <div class="o_kanban_manage_button_section">
                                    <a class="o_kanban_manage_toggle_button" href="#">
                                        <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                    </a>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span t-if="record.department.raw_value">
                                            <i class="fa fa-building-o"/> <field name="department"/>
                                        </span>
                                        <br/>
                                        <span t-if="record.position.raw_value">
                                            <i class="fa fa-user"/> <field name="position"/>
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-if="record.is_synced.raw_value" class="badge badge-success">
                                            <i class="fa fa-check"/> Synced
                                        </span>
                                        <span t-if="!record.is_synced.raw_value" class="badge badge-warning">
                                            <i class="fa fa-exclamation"/> Not Synced
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="row">
                                    <div class="col-6">
                                        <a name="action_view_attendances" type="object">
                                            <field name="total_attendances"/> Attendances
                                        </a>
                                    </div>
                                    <div class="col-6">
                                        <span class="text-warning">
                                            <field name="total_delays"/> Delays
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Employee Search View -->
    <record id="view_biometric_employee_search" model="ir.ui.view">
        <field name="name">biometric.employee.search</field>
        <field name="model">biometric.employee</field>
        <field name="arch" type="xml">
            <search string="Search Employees">
                <field name="name" string="Employee" filter_domain="['|', '|', ('name', 'ilike', self), ('employee_code', 'ilike', self), ('biometric_id', 'ilike', self)]"/>
                <field name="department"/>
                <field name="position"/>
                <field name="schedule_id"/>
                
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Synced" name="synced" domain="[('is_synced', '=', True)]"/>
                <filter string="Not Synced" name="not_synced" domain="[('is_synced', '=', False)]"/>
                <separator/>
                <filter string="With Delays" name="with_delays" domain="[('total_delays', '>', 0)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Department" name="group_department" context="{'group_by': 'department'}"/>
                    <filter string="Position" name="group_position" context="{'group_by': 'position'}"/>
                    <filter string="Schedule" name="group_schedule" context="{'group_by': 'schedule_id'}"/>
                    <filter string="Sync Status" name="group_sync" context="{'group_by': 'is_synced'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Employee Action -->
    <record id="action_biometric_employee" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">biometric.employee</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first employee!
            </p>
            <p>
                Add employees to start tracking their attendance with biometric devices.
                Each employee needs a unique biometric ID and employee code.
            </p>
        </field>
    </record>
</odoo>
