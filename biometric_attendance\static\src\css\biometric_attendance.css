/* Biometric Attendance Module Styles */

/* Main container styles */
.biometric_attendance_container {
    padding: 20px;
    background-color: #f8f9fa;
}

/* Card styles */
.biometric_card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.biometric_card_header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.biometric_card_title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

/* Status indicators */
.status_present {
    color: #28a745;
    font-weight: bold;
}

.status_late {
    color: #ffc107;
    font-weight: bold;
}

.status_absent {
    color: #dc3545;
    font-weight: bold;
}

.status_early_leave {
    color: #17a2b8;
    font-weight: bold;
}

/* Device connection status */
.device_connected {
    color: #28a745;
}

.device_disconnected {
    color: #dc3545;
}

/* Sync status indicators */
.sync_status_synced {
    background-color: #d4edda;
    color: #155724;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

.sync_status_not_synced {
    background-color: #f8d7da;
    color: #721c24;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

/* Dashboard styles */
.attendance_dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dashboard_stat_card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.dashboard_stat_number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.dashboard_stat_label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Time display */
.time_display {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: bold;
}

/* Button styles */
.btn_biometric {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s;
}

.btn_biometric:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
    text-decoration: none;
}

.btn_sync {
    background-color: #28a745;
    border-color: #28a745;
}

.btn_sync:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
}

/* Table styles */
.biometric_table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.biometric_table th,
.biometric_table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.biometric_table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.biometric_table tr:hover {
    background-color: #f8f9fa;
}

/* Form styles */
.biometric_form_group {
    margin-bottom: 20px;
}

.biometric_form_label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.biometric_form_control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

.biometric_form_control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Alert styles */
.biometric_alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.biometric_alert_success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.biometric_alert_warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.biometric_alert_danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Loading spinner */
.biometric_spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,0,0,.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .attendance_dashboard {
        grid-template-columns: 1fr;
    }
    
    .biometric_card {
        padding: 15px;
    }
    
    .dashboard_stat_number {
        font-size: 2rem;
    }
}

/* Print styles */
@media print {
    .biometric_card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn_biometric,
    .btn_sync {
        display: none;
    }
}

/* Kanban view customizations */
.o_kanban_view .oe_kanban_card.biometric_employee_card {
    border-left: 4px solid #007bff;
}

.o_kanban_view .oe_kanban_card.biometric_employee_card.not_synced {
    border-left-color: #ffc107;
}

.o_kanban_view .oe_kanban_card.biometric_device_card.connected {
    border-left: 4px solid #28a745;
}

.o_kanban_view .oe_kanban_card.biometric_device_card.disconnected {
    border-left: 4px solid #dc3545;
}

/* Form view customizations */
.o_form_view .o_form_sheet .oe_title h1 {
    color: #495057;
}

/* List view customizations */
.o_list_view .o_data_row.decoration-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.o_list_view .o_data_row.decoration-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.o_list_view .o_data_row.decoration-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.o_list_view .o_data_row.decoration-info {
    background-color: rgba(23, 162, 184, 0.1);
}
