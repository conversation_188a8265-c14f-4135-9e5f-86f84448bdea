#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتأكد من صحة موديول Biometric Attendance
"""

import os
import sys

def test_module_structure():
    """اختبار هيكل الموديول"""
    print("🔍 اختبار هيكل الموديول...")
    
    required_files = [
        'biometric_attendance/__init__.py',
        'biometric_attendance/__manifest__.py',
        'biometric_attendance/README.rst',
        'biometric_attendance/models/__init__.py',
        'biometric_attendance/views/biometric_menus.xml',
        'biometric_attendance/security/ir.model.access.csv',
        'biometric_attendance/data/biometric_data.xml',
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ جميع الملفات الأساسية موجودة")
        return True

def test_manifest_syntax():
    """اختبار صحة ملف __manifest__.py"""
    print("🔍 اختبار ملف __manifest__.py...")
    
    try:
        with open('biometric_attendance/__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # اختبار أساسي للصيغة
        if 'name' in content and 'version' in content and 'depends' in content:
            print("✅ ملف __manifest__.py يبدو صحيحاً")
            return True
        else:
            print("❌ ملف __manifest__.py يحتوي على مشاكل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة __manifest__.py: {e}")
        return False

def test_xml_files():
    """اختبار ملفات XML"""
    print("🔍 اختبار ملفات XML...")
    
    xml_files = [
        'biometric_attendance/data/biometric_data.xml',
        'biometric_attendance/data/biometric_cron.xml',
        'biometric_attendance/views/biometric_menus.xml',
        'biometric_attendance/security/biometric_security.xml',
    ]
    
    for xml_file in xml_files:
        if os.path.exists(xml_file):
            try:
                with open(xml_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # اختبار أساسي للصيغة
                if '<?xml' in content and '<odoo>' in content:
                    print(f"✅ {xml_file}")
                else:
                    print(f"❌ {xml_file} - صيغة XML غير صحيحة")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ في قراءة {xml_file}: {e}")
                return False
        else:
            print(f"⚠️  {xml_file} غير موجود")
    
    return True

def test_python_syntax():
    """اختبار صحة ملفات Python"""
    print("🔍 اختبار ملفات Python...")
    
    python_files = [
        'biometric_attendance/models/biometric_employee.py',
        'biometric_attendance/models/biometric_attendance.py',
        'biometric_attendance/models/biometric_device.py',
        'biometric_attendance/lib/zkteco_client.py',
    ]
    
    for py_file in python_files:
        if os.path.exists(py_file):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # اختبار أساسي للصيغة
                if 'from odoo import' in content or 'import' in content:
                    print(f"✅ {py_file}")
                else:
                    print(f"⚠️  {py_file} - قد يحتوي على مشاكل")
                    
            except Exception as e:
                print(f"❌ خطأ في قراءة {py_file}: {e}")
                return False
        else:
            print(f"⚠️  {py_file} غير موجود")
    
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار موديول Biometric Attendance")
    print("=" * 50)
    
    tests = [
        test_module_structure,
        test_manifest_syntax,
        test_xml_files,
        test_python_syntax,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! الموديول جاهز للتثبيت")
        print("\n📋 خطوات التثبيت:")
        print("1. انسخ مجلد biometric_attendance إلى مجلد addons")
        print("2. أعد تشغيل Odoo")
        print("3. حدث قائمة التطبيقات")
        print("4. ثبت الموديول")
        return True
    else:
        print("⚠️  يرجى إصلاح المشاكل قبل التثبيت")
        return False

if __name__ == "__main__":
    main()
