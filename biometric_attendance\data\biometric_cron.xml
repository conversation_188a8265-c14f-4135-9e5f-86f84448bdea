<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Auto Sync Attendance Data -->
    <record id="cron_auto_sync_attendance" model="ir.cron">
        <field name="name">Auto Sync Biometric Attendance</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
devices = model.search([('active', '=', True), ('device_type', '=', 'zkteco')])
for device in devices:
    try:
        device.test_connection()
        device.sync_attendance()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error("Auto sync failed for device %s: %s" % (device.name, str(e)))
        ]]></field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Process Daily Attendance -->
    <record id="cron_process_daily_attendance" model="ir.cron">
        <field name="name">Process Daily Attendance</field>
        <field name="model_id" ref="model_biometric_attendance_processor"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
from datetime import date, timedelta
yesterday = date.today() - timedelta(days=1)
model.process_daily_attendance(yesterday)
        ]]></field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Auto Sync Employees (Weekly) -->
    <record id="cron_auto_sync_employees" model="ir.cron">
        <field name="name">Auto Sync Biometric Employees</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
devices = model.search([('active', '=', True), ('device_type', '=', 'zkteco')])
for device in devices:
    try:
        device.test_connection()
        device.sync_employees()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error("Employee sync failed for device %s: %s" % (device.name, str(e)))
        ]]></field>
        <field name="interval_number">1</field>
        <field name="interval_type">weeks</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Test Device Connections -->
    <record id="cron_test_device_connections" model="ir.cron">
        <field name="name">Test Biometric Device Connections</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
devices = model.search([('active', '=', True)])
for device in devices:
    try:
        device.test_connection()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.warning("Connection test failed for device %s: %s" % (device.name, str(e)))
        ]]></field>
        <field name="interval_number">30</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Clean Old Attendance Records (Monthly) -->
    <record id="cron_clean_old_attendance" model="ir.cron">
        <field name="name">Clean Old Attendance Records</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
from datetime import date, timedelta
cutoff_date = date.today() - timedelta(days=730)
old_records = model.search([('attendance_date', '<', cutoff_date)])
if old_records:
    import logging
    _logger = logging.getLogger(__name__)
    _logger.info("Cleaning %s old attendance records" % len(old_records))
    old_records.unlink()
        ]]></field>
        <field name="interval_number">1</field>
        <field name="interval_type">months</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Generate Monthly Reports (Monthly) -->
    <record id="cron_generate_monthly_reports" model="ir.cron">
        <field name="name">Generate Monthly Attendance Reports</field>
        <field name="model_id" ref="model_biometric_attendance_processor"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
from datetime import date, timedelta

today = date.today()
first_day_this_month = today.replace(day=1)
last_day_last_month = first_day_this_month - timedelta(days=1)
first_day_last_month = last_day_last_month.replace(day=1)

employees = env['biometric.employee'].search([('active', '=', True)])

for employee in employees:
    try:
        summary = model.generate_attendance_summary(
            employee.id, first_day_last_month, last_day_last_month
        )
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info("Monthly summary for %s: %s" % (employee.name, summary))
    except Exception as e:
        continue
        ]]></field>
        <field name="interval_number">1</field>
        <field name="interval_type">months</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="doall" eval="False"/>
    </record>
</odoo>
