<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Auto Sync Attendance Data -->
    <record id="cron_auto_sync_attendance" model="ir.cron">
        <field name="name">Auto Sync Biometric Attendance</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code">
# Auto sync attendance from all active devices
devices = model.search([('active', '=', True), ('device_type', '=', 'zkteco')])
for device in devices:
    try:
        if device.test_connection():
            device.sync_attendance()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error(f"Auto sync failed for device {device.name}: {str(e)}")
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Process Daily Attendance -->
    <record id="cron_process_daily_attendance" model="ir.cron">
        <field name="name">Process Daily Attendance</field>
        <field name="model_id" ref="model_biometric_attendance_processor"/>
        <field name="state">code</field>
        <field name="code">
# Process attendance for yesterday
from datetime import date, timedelta
yesterday = date.today() - timedelta(days=1)
model.process_daily_attendance(yesterday)
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
        <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).replace(hour=1, minute=0, second=0)"/>
    </record>

    <!-- Auto Sync Employees (Weekly) -->
    <record id="cron_auto_sync_employees" model="ir.cron">
        <field name="name">Auto Sync Biometric Employees</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code">
# Auto sync employees from all active devices (weekly)
devices = model.search([('active', '=', True), ('device_type', '=', 'zkteco')])
for device in devices:
    try:
        if device.test_connection():
            device.sync_employees()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error(f"Employee sync failed for device {device.name}: {str(e)}")
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">weeks</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
        <field name="nextcall" eval="(DateTime.now() + timedelta(days=7)).replace(hour=2, minute=0, second=0)"/>
    </record>

    <!-- Test Device Connections -->
    <record id="cron_test_device_connections" model="ir.cron">
        <field name="name">Test Biometric Device Connections</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code">
# Test connection to all active devices
devices = model.search([('active', '=', True)])
for device in devices:
    try:
        device.test_connection()
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.warning(f"Connection test failed for device {device.name}: {str(e)}")
        </field>
        <field name="interval_number">30</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Clean Old Attendance Records (Monthly) -->
    <record id="cron_clean_old_attendance" model="ir.cron">
        <field name="name">Clean Old Attendance Records</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="state">code</field>
        <field name="code">
# Clean attendance records older than 2 years
from datetime import date, timedelta
cutoff_date = date.today() - timedelta(days=730)  # 2 years
old_records = model.search([('attendance_date', '&lt;', cutoff_date)])
if old_records:
    import logging
    _logger = logging.getLogger(__name__)
    _logger.info(f"Cleaning {len(old_records)} old attendance records")
    old_records.unlink()
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">months</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="doall" eval="False"/>
        <field name="nextcall" eval="(DateTime.now() + timedelta(days=30)).replace(hour=3, minute=0, second=0)"/>
    </record>

    <!-- Generate Monthly Reports (Monthly) -->
    <record id="cron_generate_monthly_reports" model="ir.cron">
        <field name="name">Generate Monthly Attendance Reports</field>
        <field name="model_id" ref="model_biometric_attendance_processor"/>
        <field name="state">code</field>
        <field name="code">
# Generate monthly reports for all employees
from datetime import date, timedelta
import calendar

# Get last month
today = date.today()
first_day_this_month = today.replace(day=1)
last_day_last_month = first_day_this_month - timedelta(days=1)
first_day_last_month = last_day_last_month.replace(day=1)

# Get all active employees
employees = env['biometric.employee'].search([('active', '=', True)])

for employee in employees:
    try:
        summary = model.generate_attendance_summary(
            employee.id, first_day_last_month, last_day_last_month
        )
        # Here you could send email reports or save to files
        # For now, just log the summary
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info(f"Monthly summary for {employee.name}: {summary}")
    except Exception as e:
        continue
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">months</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="doall" eval="False"/>
        <field name="nextcall" eval="(DateTime.now() + timedelta(days=30)).replace(day=1, hour=4, minute=0, second=0)"/>
    </record>
</odoo>
