# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
import pytz


class BiometricAttendance(models.Model):
    _name = 'biometric.attendance'
    _description = 'Biometric Attendance Record'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'attendance_date desc, check_time desc'
    _rec_name = 'display_name'

    # Basic Information
    employee_id = fields.Many2one('biometric.employee', string='Employee', required=True, index=True)
    attendance_date = fields.Date(string='Date', required=True, index=True)
    check_time = fields.Datetime(string='Check Time', required=True, index=True)
    
    # Attendance Type
    attendance_type = fields.Selection([
        ('check_in', 'Check In'),
        ('check_out', 'Check Out'),
        ('break_out', 'Break Out'),
        ('break_in', 'Break In'),
    ], string='Type', required=True, default='check_in')
    
    # Device Information
    device_id = fields.Many2one('biometric.device', string='Device')
    device_user_id = fields.Char(string='Device User ID')
    verification_type = fields.Selection([
        ('fingerprint', 'Fingerprint'),
        ('face', 'Face Recognition'),
        ('card', 'Card'),
        ('password', 'Password'),
    ], string='Verification Type', default='fingerprint')
    
    # Status and Calculations
    status = fields.Selection([
        ('present', 'Present'),
        ('late', 'Late'),
        ('absent', 'Absent'),
        ('early_leave', 'Early Leave'),
    ], string='Status', compute='_compute_status', store=True)
    
    is_late = fields.Boolean(string='Is Late', compute='_compute_status', store=True)
    late_minutes = fields.Integer(string='Late Minutes', compute='_compute_status', store=True)
    
    # Working Hours
    scheduled_in = fields.Datetime(string='Scheduled Check In', compute='_compute_scheduled_times', store=True)
    scheduled_out = fields.Datetime(string='Scheduled Check Out', compute='_compute_scheduled_times', store=True)
    actual_in = fields.Datetime(string='Actual Check In', compute='_compute_actual_times', store=True)
    actual_out = fields.Datetime(string='Actual Check Out', compute='_compute_actual_times', store=True)
    
    worked_hours = fields.Float(string='Worked Hours', compute='_compute_worked_hours', store=True)
    overtime_hours = fields.Float(string='Overtime Hours', compute='_compute_worked_hours', store=True)
    
    # Additional Information
    notes = fields.Text(string='Notes')
    is_manual = fields.Boolean(string='Manual Entry', default=False)
    is_synced = fields.Boolean(string='Synced from Device', default=True)
    
    # Display Name
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
    
    @api.depends('employee_id', 'attendance_date', 'check_time', 'attendance_type')
    def _compute_display_name(self):
        for record in self:
            if record.employee_id and record.check_time:
                record.display_name = f"{record.employee_id.name} - {record.attendance_date} - {record.attendance_type}"
            else:
                record.display_name = "New Attendance"
    
    @api.depends('employee_id', 'attendance_date')
    def _compute_scheduled_times(self):
        for record in self:
            if record.employee_id and record.employee_id.schedule_id and record.attendance_date:
                schedule = record.employee_id.schedule_id
                weekday = record.attendance_date.weekday()
                
                # Get schedule line for this weekday
                schedule_line = schedule.line_ids.filtered(lambda l: l.dayofweek == str(weekday))
                if schedule_line:
                    line = schedule_line[0]
                    # Convert time to datetime
                    date_str = record.attendance_date.strftime('%Y-%m-%d')
                    record.scheduled_in = datetime.strptime(f"{date_str} {line.hour_from:02.0f}:{int((line.hour_from % 1) * 60):02d}", '%Y-%m-%d %H:%M')
                    record.scheduled_out = datetime.strptime(f"{date_str} {line.hour_to:02.0f}:{int((line.hour_to % 1) * 60):02d}", '%Y-%m-%d %H:%M')
                else:
                    record.scheduled_in = False
                    record.scheduled_out = False
            else:
                record.scheduled_in = False
                record.scheduled_out = False
    
    @api.depends('employee_id', 'attendance_date')
    def _compute_actual_times(self):
        for record in self:
            if record.employee_id and record.attendance_date:
                # Get all attendances for this employee on this date
                attendances = self.search([
                    ('employee_id', '=', record.employee_id.id),
                    ('attendance_date', '=', record.attendance_date)
                ]).sorted('check_time')
                
                check_ins = attendances.filtered(lambda a: a.attendance_type == 'check_in')
                check_outs = attendances.filtered(lambda a: a.attendance_type == 'check_out')
                
                record.actual_in = check_ins[0].check_time if check_ins else False
                record.actual_out = check_outs[-1].check_time if check_outs else False
            else:
                record.actual_in = False
                record.actual_out = False
    
    @api.depends('scheduled_in', 'actual_in', 'actual_out')
    def _compute_status(self):
        for record in self:
            if not record.actual_in:
                record.status = 'absent'
                record.is_late = False
                record.late_minutes = 0
            elif record.scheduled_in and record.actual_in:
                # Calculate if late
                if record.actual_in > record.scheduled_in:
                    record.is_late = True
                    record.status = 'late'
                    delta = record.actual_in - record.scheduled_in
                    record.late_minutes = int(delta.total_seconds() / 60)
                else:
                    record.is_late = False
                    record.status = 'present'
                    record.late_minutes = 0
                
                # Check for early leave
                if record.scheduled_out and record.actual_out and record.actual_out < record.scheduled_out:
                    record.status = 'early_leave'
            else:
                record.status = 'present'
                record.is_late = False
                record.late_minutes = 0
    
    @api.depends('actual_in', 'actual_out', 'scheduled_in', 'scheduled_out')
    def _compute_worked_hours(self):
        for record in self:
            if record.actual_in and record.actual_out:
                delta = record.actual_out - record.actual_in
                record.worked_hours = delta.total_seconds() / 3600
                
                # Calculate overtime
                if record.scheduled_in and record.scheduled_out:
                    scheduled_hours = (record.scheduled_out - record.scheduled_in).total_seconds() / 3600
                    record.overtime_hours = max(0, record.worked_hours - scheduled_hours)
                else:
                    record.overtime_hours = 0
            else:
                record.worked_hours = 0
                record.overtime_hours = 0
    
    @api.constrains('employee_id', 'attendance_date', 'check_time', 'attendance_type')
    def _check_duplicate_attendance(self):
        for record in self:
            if record.employee_id and record.attendance_date and record.check_time:
                existing = self.search([
                    ('employee_id', '=', record.employee_id.id),
                    ('attendance_date', '=', record.attendance_date),
                    ('check_time', '=', record.check_time),
                    ('attendance_type', '=', record.attendance_type),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(_('Duplicate attendance record found for the same employee, date, time and type!'))
    
    @api.model
    def create_from_device(self, employee_biometric_id, check_time, attendance_type='check_in', device_id=None):
        """Create attendance record from device data"""
        employee = self.env['biometric.employee'].search([('biometric_id', '=', employee_biometric_id)], limit=1)
        if not employee:
            return False
        
        # Convert check_time to date
        if isinstance(check_time, str):
            check_time = datetime.strptime(check_time, '%Y-%m-%d %H:%M:%S')
        
        attendance_date = check_time.date()
        
        # Check if record already exists
        existing = self.search([
            ('employee_id', '=', employee.id),
            ('check_time', '=', check_time),
            ('attendance_type', '=', attendance_type)
        ])
        if existing:
            return existing
        
        # Create new record
        return self.create({
            'employee_id': employee.id,
            'attendance_date': attendance_date,
            'check_time': check_time,
            'attendance_type': attendance_type,
            'device_id': device_id,
            'is_synced': True,
        })
