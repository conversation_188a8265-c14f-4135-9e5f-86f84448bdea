<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Default System Settings -->
    <record id="default_biometric_settings" model="biometric.settings">
        <field name="name">Default Biometric Settings</field>
        <field name="auto_sync_enabled" eval="True"/>
        <field name="sync_interval_hours">1</field>
        <field name="grace_period_minutes">15</field>
        <field name="overtime_threshold_hours">8.0</field>
        <field name="monday_working" eval="True"/>
        <field name="tuesday_working" eval="True"/>
        <field name="wednesday_working" eval="True"/>
        <field name="thursday_working" eval="True"/>
        <field name="friday_working" eval="True"/>
        <field name="saturday_working" eval="False"/>
        <field name="sunday_working" eval="False"/>
        <field name="default_work_start">8.0</field>
        <field name="default_work_end">17.0</field>
        <field name="break_enabled" eval="True"/>
        <field name="default_break_start">12.0</field>
        <field name="default_break_end">13.0</field>
        <field name="late_notification_enabled" eval="True"/>
        <field name="absence_notification_enabled" eval="True"/>
        <field name="default_report_period">monthly</field>
        <field name="timezone">UTC</field>
        <field name="date_format">%d/%m/%Y</field>
        <field name="time_format">%H:%M</field>
    </record>

    <!-- Default Work Schedule -->
    <record id="default_work_schedule" model="biometric.schedule">
        <field name="name">Standard Work Schedule</field>
        <field name="code">DEFAULT</field>
        <field name="description">Standard 8-hour work schedule, Monday to Friday</field>
        <field name="grace_period_minutes">15</field>
        <field name="overtime_threshold">8.0</field>
        <field name="active" eval="True"/>
    </record>

    <!-- Default Schedule Lines -->
    <record id="schedule_line_monday" model="biometric.schedule.line">
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="dayofweek">0</field>
        <field name="hour_from">8.0</field>
        <field name="hour_to">17.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="schedule_line_tuesday" model="biometric.schedule.line">
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="dayofweek">1</field>
        <field name="hour_from">8.0</field>
        <field name="hour_to">17.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="schedule_line_wednesday" model="biometric.schedule.line">
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="dayofweek">2</field>
        <field name="hour_from">8.0</field>
        <field name="hour_to">17.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="schedule_line_thursday" model="biometric.schedule.line">
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="dayofweek">3</field>
        <field name="hour_from">8.0</field>
        <field name="hour_to">17.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="schedule_line_friday" model="biometric.schedule.line">
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="dayofweek">4</field>
        <field name="hour_from">8.0</field>
        <field name="hour_to">17.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="schedule_line_saturday" model="biometric.schedule.line">
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="dayofweek">5</field>
        <field name="hour_from">8.0</field>
        <field name="hour_to">17.0</field>
        <field name="is_working_day" eval="False"/>
    </record>

    <record id="schedule_line_sunday" model="biometric.schedule.line">
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="dayofweek">6</field>
        <field name="hour_from">8.0</field>
        <field name="hour_to">17.0</field>
        <field name="is_working_day" eval="False"/>
    </record>

    <!-- Shift Work Schedule -->
    <record id="shift_work_schedule" model="biometric.schedule">
        <field name="name">Shift Work Schedule</field>
        <field name="code">SHIFT</field>
        <field name="description">12-hour shift schedule</field>
        <field name="grace_period_minutes">30</field>
        <field name="overtime_threshold">12.0</field>
        <field name="active" eval="True"/>
    </record>

    <!-- Shift Schedule Lines -->
    <record id="shift_schedule_line_monday" model="biometric.schedule.line">
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="dayofweek">0</field>
        <field name="hour_from">6.0</field>
        <field name="hour_to">18.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="shift_schedule_line_tuesday" model="biometric.schedule.line">
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="dayofweek">1</field>
        <field name="hour_from">6.0</field>
        <field name="hour_to">18.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="shift_schedule_line_wednesday" model="biometric.schedule.line">
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="dayofweek">2</field>
        <field name="hour_from">6.0</field>
        <field name="hour_to">18.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="shift_schedule_line_thursday" model="biometric.schedule.line">
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="dayofweek">3</field>
        <field name="hour_from">6.0</field>
        <field name="hour_to">18.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="shift_schedule_line_friday" model="biometric.schedule.line">
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="dayofweek">4</field>
        <field name="hour_from">6.0</field>
        <field name="hour_to">18.0</field>
        <field name="break_hour_from">12.0</field>
        <field name="break_hour_to">13.0</field>
        <field name="is_working_day" eval="True"/>
    </record>

    <record id="shift_schedule_line_saturday" model="biometric.schedule.line">
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="dayofweek">5</field>
        <field name="hour_from">6.0</field>
        <field name="hour_to">18.0</field>
        <field name="is_working_day" eval="False"/>
    </record>

    <record id="shift_schedule_line_sunday" model="biometric.schedule.line">
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="dayofweek">6</field>
        <field name="hour_from">6.0</field>
        <field name="hour_to">18.0</field>
        <field name="is_working_day" eval="False"/>
    </record>

    <!-- Sample Holidays -->
    <record id="holiday_new_year" model="biometric.holiday">
        <field name="name">New Year's Day</field>
        <field name="date" eval="(DateTime.now().replace(month=1, day=1)).date()"/>
        <field name="holiday_type">public</field>
        <field name="is_recurring" eval="True"/>
        <field name="description">New Year's Day - Public Holiday</field>
        <field name="active" eval="True"/>
    </record>

    <record id="holiday_christmas" model="biometric.holiday">
        <field name="name">Christmas Day</field>
        <field name="date" eval="(DateTime.now().replace(month=12, day=25)).date()"/>
        <field name="holiday_type">religious</field>
        <field name="is_recurring" eval="True"/>
        <field name="description">Christmas Day - Religious Holiday</field>
        <field name="active" eval="True"/>
    </record>
</odoo>
