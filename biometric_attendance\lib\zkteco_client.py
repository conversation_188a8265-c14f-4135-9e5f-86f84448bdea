# -*- coding: utf-8 -*-

import socket
import struct
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


class ZKTecoClient:
    """ZKTeco device communication client"""
    
    # Command constants
    CMD_CONNECT = 1000
    CMD_EXIT = 1001
    CMD_ENABLEDEVICE = 1002
    CMD_DISABLEDEVICE = 1003
    CMD_RESTART = 1004
    CMD_POWEROFF = 1005
    CMD_SLEEP = 1006
    CMD_RESUME = 1007
    CMD_TEST_TEMP = 1011
    CMD_TESTVOICE = 1017
    CMD_VERSION = 1100
    CMD_CHANGE_SPEED = 1101
    CMD_AUTH = 1102
    CMD_PREPARE_DATA = 1500
    CMD_DATA = 1501
    CMD_FREE_DATA = 1502
    CMD_PREPARE_BUFFER = 1503
    CMD_READ_BUFFER = 1504
    CMD_USER_WRQ = 8
    CMD_USERTEMP_RRQ = 9
    CMD_USERTEMP_WRQ = 10
    CMD_OPTIONS_RRQ = 11
    CMD_OPTIONS_WRQ = 12
    CMD_ATTLOG_RRQ = 13
    CMD_CLEAR_DATA = 14
    CMD_CLEAR_ATTLOG = 15
    CMD_DELETE_USER = 18
    CMD_DELETE_USERTEMP = 19
    CMD_CLEAR_ADMIN = 20
    CMD_ENABLE_CLOCK = 57
    CMD_STARTVERIFY = 60
    CMD_STARTENROLL = 61
    CMD_CANCELCAPTURE = 62
    CMD_STATE_RRQ = 64
    CMD_WRITE_LCD = 66
    CMD_CLEAR_LCD = 67
    CMD_GET_PINWIDTH = 69
    CMD_SMS_WRQ = 70
    CMD_SMS_RRQ = 71
    CMD_DELETE_SMS = 72
    CMD_ENABLE_DEVICE = 73
    CMD_GET_PLATFORM = 100
    CMD_GET_FWVERSION = 102
    CMD_GET_SERIALNUMBER = 103
    CMD_GET_DEVICENAME = 104
    CMD_SET_DEVICENAME = 105
    CMD_GET_WORKCODE = 106
    CMD_SET_WORKCODE = 107
    CMD_GET_SSR = 108
    CMD_SET_SSR = 109
    CMD_GET_PINWIDTH = 110
    CMD_GET_FACE_VERSION = 111
    CMD_GET_FP_VERSION = 112
    CMD_GET_TIME = 201
    CMD_SET_TIME = 202
    
    def __init__(self, ip, port=4370, timeout=30, password=0):
        self.ip = ip
        self.port = port
        self.timeout = timeout
        self.password = password
        self.socket = None
        self.session_id = 0
        self.reply_id = 0
        self.is_connected = False
        
    def connect(self):
        """Connect to the device"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.ip, self.port))
            
            # Send connect command
            command = self._create_header(self.CMD_CONNECT, self.session_id, self.reply_id, b'')
            self.socket.send(command)
            
            # Receive response
            response = self.socket.recv(1024)
            if len(response) >= 16:
                reply_id, command_id, checksum, session_id, reply_data = self._parse_header(response)
                if command_id == self.CMD_CONNECT:
                    self.session_id = session_id
                    self.is_connected = True
                    _logger.info(f"Connected to device {self.ip}:{self.port}")
                    return True
            
            return False
            
        except Exception as e:
            _logger.error(f"Connection failed: {str(e)}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Disconnect from the device"""
        try:
            if self.socket and self.is_connected:
                command = self._create_header(self.CMD_EXIT, self.session_id, self.reply_id, b'')
                self.socket.send(command)
                self.socket.close()
            self.is_connected = False
            _logger.info(f"Disconnected from device {self.ip}:{self.port}")
        except Exception as e:
            _logger.error(f"Disconnect error: {str(e)}")
    
    def get_users(self):
        """Get all users from the device"""
        if not self.is_connected:
            return []
        
        try:
            # Request user data
            command = self._create_header(self.CMD_USER_WRQ, self.session_id, self.reply_id, b'')
            self.socket.send(command)
            
            # Receive response
            response = self.socket.recv(1024)
            reply_id, command_id, checksum, session_id, reply_data = self._parse_header(response)
            
            if command_id == self.CMD_PREPARE_DATA:
                # Get data size
                data_size = struct.unpack('<I', reply_data[:4])[0]
                
                # Request actual data
                command = self._create_header(self.CMD_DATA, self.session_id, self.reply_id, b'')
                self.socket.send(command)
                
                # Receive user data
                users_data = b''
                while len(users_data) < data_size:
                    chunk = self.socket.recv(min(1024, data_size - len(users_data)))
                    users_data += chunk
                
                # Parse users
                users = self._parse_users(users_data)
                
                # Free data
                command = self._create_header(self.CMD_FREE_DATA, self.session_id, self.reply_id, b'')
                self.socket.send(command)
                
                return users
            
            return []
            
        except Exception as e:
            _logger.error(f"Error getting users: {str(e)}")
            return []
    
    def get_attendance(self):
        """Get attendance records from the device"""
        if not self.is_connected:
            return []
        
        try:
            # Request attendance data
            command = self._create_header(self.CMD_ATTLOG_RRQ, self.session_id, self.reply_id, b'')
            self.socket.send(command)
            
            # Receive response
            response = self.socket.recv(1024)
            reply_id, command_id, checksum, session_id, reply_data = self._parse_header(response)
            
            if command_id == self.CMD_PREPARE_DATA:
                # Get data size
                data_size = struct.unpack('<I', reply_data[:4])[0]
                
                # Request actual data
                command = self._create_header(self.CMD_DATA, self.session_id, self.reply_id, b'')
                self.socket.send(command)
                
                # Receive attendance data
                att_data = b''
                while len(att_data) < data_size:
                    chunk = self.socket.recv(min(1024, data_size - len(att_data)))
                    att_data += chunk
                
                # Parse attendance records
                records = self._parse_attendance(att_data)
                
                # Free data
                command = self._create_header(self.CMD_FREE_DATA, self.session_id, self.reply_id, b'')
                self.socket.send(command)
                
                return records
            
            return []
            
        except Exception as e:
            _logger.error(f"Error getting attendance: {str(e)}")
            return []
    
    def get_device_info(self):
        """Get device information"""
        if not self.is_connected:
            return {}
        
        info = {}
        
        try:
            # Get firmware version
            command = self._create_header(self.CMD_GET_FWVERSION, self.session_id, self.reply_id, b'')
            self.socket.send(command)
            response = self.socket.recv(1024)
            _, _, _, _, reply_data = self._parse_header(response)
            if reply_data:
                info['firmware_version'] = reply_data.decode('utf-8', errors='ignore').strip('\x00')
            
            # Get serial number
            command = self._create_header(self.CMD_GET_SERIALNUMBER, self.session_id, self.reply_id, b'')
            self.socket.send(command)
            response = self.socket.recv(1024)
            _, _, _, _, reply_data = self._parse_header(response)
            if reply_data:
                info['serial_number'] = reply_data.decode('utf-8', errors='ignore').strip('\x00')
            
            # Get device name
            command = self._create_header(self.CMD_GET_DEVICENAME, self.session_id, self.reply_id, b'')
            self.socket.send(command)
            response = self.socket.recv(1024)
            _, _, _, _, reply_data = self._parse_header(response)
            if reply_data:
                info['device_name'] = reply_data.decode('utf-8', errors='ignore').strip('\x00')
            
        except Exception as e:
            _logger.error(f"Error getting device info: {str(e)}")
        
        return info
    
    def _create_header(self, command, session_id, reply_id, data):
        """Create command header"""
        checksum = 0
        if data:
            checksum = sum(data) % 65536
        
        header = struct.pack('<HHHH', reply_id, command, checksum, session_id)
        return header + data
    
    def _parse_header(self, data):
        """Parse response header"""
        if len(data) < 8:
            return None, None, None, None, b''
        
        reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
        reply_data = data[8:] if len(data) > 8 else b''
        
        return reply_id, command_id, checksum, session_id, reply_data
    
    def _parse_users(self, data):
        """Parse users data"""
        users = []
        # Each user record is typically 28 bytes
        record_size = 28
        
        for i in range(0, len(data), record_size):
            if i + record_size <= len(data):
                record = data[i:i + record_size]
                try:
                    # Parse user record (simplified)
                    user_id = struct.unpack('<H', record[0:2])[0]
                    # Name is typically at offset 8, 24 bytes
                    name = record[8:32].decode('utf-8', errors='ignore').strip('\x00')
                    
                    if user_id and name:
                        users.append({
                            'biometric_id': str(user_id),
                            'name': name,
                            'card_number': None,
                            'employee_code': str(user_id)
                        })
                except:
                    continue
        
        return users
    
    def _parse_attendance(self, data):
        """Parse attendance data"""
        records = []
        # Each attendance record is typically 16 bytes
        record_size = 16
        
        for i in range(0, len(data), record_size):
            if i + record_size <= len(data):
                record = data[i:i + record_size]
                try:
                    # Parse attendance record
                    user_id, timestamp, verify_type, in_out_type = struct.unpack('<HIBB', record[:10])
                    
                    # Convert timestamp to datetime
                    check_time = datetime.fromtimestamp(timestamp)
                    
                    # Determine attendance type
                    att_type = 'check_in' if in_out_type == 0 else 'check_out'
                    
                    # Determine verification type
                    verification_types = {0: 'password', 1: 'fingerprint', 2: 'card', 3: 'face'}
                    verification = verification_types.get(verify_type, 'fingerprint')
                    
                    records.append({
                        'user_id': str(user_id),
                        'check_time': check_time,
                        'type': att_type,
                        'verification_type': verification
                    })
                except:
                    continue
        
        return records
