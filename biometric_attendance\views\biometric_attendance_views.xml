<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Attendance Tree View -->
    <record id="view_biometric_attendance_tree" model="ir.ui.view">
        <field name="name">biometric.attendance.tree</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <tree string="Attendance Records" decoration-success="status == 'present'" 
                  decoration-warning="status == 'late'" decoration-danger="status == 'absent'" 
                  decoration-info="status == 'early_leave'">
                <field name="employee_id"/>
                <field name="attendance_date"/>
                <field name="check_time"/>
                <field name="attendance_type"/>
                <field name="status"/>
                <field name="late_minutes" attrs="{'invisible': [('is_late', '=', False)]}"/>
                <field name="worked_hours" widget="float_time"/>
                <field name="device_id"/>
                <field name="verification_type"/>
                <field name="is_late" invisible="1"/>
                <field name="is_manual" invisible="1"/>
                <field name="is_synced" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Attendance Form View -->
    <record id="view_biometric_attendance_form" model="ir.ui.view">
        <field name="name">biometric.attendance.form</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <form string="Attendance Record">
                <header>
                    <field name="status" widget="statusbar" statusbar_visible="present,late,absent,early_leave"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_employee" type="object" class="oe_stat_button" icon="fa-user">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Employee</span>
                            </div>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Manual Entry" bg_color="bg-info" 
                            attrs="{'invisible': [('is_manual', '=', False)]}"/>
                    <widget name="web_ribbon" title="Late" bg_color="bg-warning" 
                            attrs="{'invisible': [('is_late', '=', False)]}"/>
                    
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="employee_id" required="1"/>
                            <field name="attendance_date" required="1"/>
                            <field name="check_time" required="1"/>
                            <field name="attendance_type" required="1"/>
                        </group>
                        <group name="device_info" string="Device Information">
                            <field name="device_id"/>
                            <field name="device_user_id"/>
                            <field name="verification_type"/>
                            <field name="is_synced" readonly="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="schedule_info" string="Schedule Information">
                            <field name="scheduled_in" readonly="1"/>
                            <field name="scheduled_out" readonly="1"/>
                            <field name="actual_in" readonly="1"/>
                            <field name="actual_out" readonly="1"/>
                        </group>
                        <group name="time_info" string="Time Calculations">
                            <field name="is_late" readonly="1"/>
                            <field name="late_minutes" readonly="1" attrs="{'invisible': [('is_late', '=', False)]}"/>
                            <field name="worked_hours" widget="float_time" readonly="1"/>
                            <field name="overtime_hours" widget="float_time" readonly="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="additional_info" string="Additional Information">
                            <field name="is_manual"/>
                            <field name="notes"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Attendance Calendar View -->
    <record id="view_biometric_attendance_calendar" model="ir.ui.view">
        <field name="name">biometric.attendance.calendar</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <calendar string="Attendance Calendar" date_start="check_time" color="employee_id" mode="month">
                <field name="employee_id"/>
                <field name="attendance_type"/>
                <field name="status"/>
            </calendar>
        </field>
    </record>

    <!-- Attendance Pivot View -->
    <record id="view_biometric_attendance_pivot" model="ir.ui.view">
        <field name="name">biometric.attendance.pivot</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <pivot string="Attendance Analysis">
                <field name="employee_id" type="row"/>
                <field name="attendance_date" type="col" interval="month"/>
                <field name="worked_hours" type="measure"/>
                <field name="late_minutes" type="measure"/>
                <field name="overtime_hours" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Attendance Graph View -->
    <record id="view_biometric_attendance_graph" model="ir.ui.view">
        <field name="name">biometric.attendance.graph</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <graph string="Attendance Statistics" type="bar">
                <field name="attendance_date" type="row" interval="week"/>
                <field name="worked_hours" type="measure"/>
                <field name="overtime_hours" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Attendance Search View -->
    <record id="view_biometric_attendance_search" model="ir.ui.view">
        <field name="name">biometric.attendance.search</field>
        <field name="model">biometric.attendance</field>
        <field name="arch" type="xml">
            <search string="Search Attendance">
                <field name="employee_id" string="Employee"/>
                <field name="attendance_date"/>
                <field name="device_id"/>
                
                <filter string="Today" name="today" domain="[('attendance_date', '=', context_today())]"/>
                <filter string="This Week" name="this_week" domain="[]"/>
                <filter string="This Month" name="this_month" domain="[]"/>
                <separator/>
                <filter string="Present" name="present" domain="[('status', '=', 'present')]"/>
                <filter string="Late" name="late" domain="[('status', '=', 'late')]"/>
                <filter string="Absent" name="absent" domain="[('status', '=', 'absent')]"/>
                <filter string="Early Leave" name="early_leave" domain="[('status', '=', 'early_leave')]"/>
                <separator/>
                <filter string="Check In" name="check_in" domain="[('attendance_type', '=', 'check_in')]"/>
                <filter string="Check Out" name="check_out" domain="[('attendance_type', '=', 'check_out')]"/>
                <separator/>
                <filter string="Manual Entry" name="manual" domain="[('is_manual', '=', True)]"/>
                <filter string="Device Entry" name="device" domain="[('is_manual', '=', False)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Employee" name="group_employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'attendance_date'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Type" name="group_type" context="{'group_by': 'attendance_type'}"/>
                    <filter string="Device" name="group_device" context="{'group_by': 'device_id'}"/>
                    <filter string="Month" name="group_month" context="{'group_by': 'attendance_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Attendance Action -->
    <record id="action_biometric_attendance" model="ir.actions.act_window">
        <field name="name">Attendance Records</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">tree,form,calendar,pivot,graph</field>
        <field name="context">{'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No attendance records found!
            </p>
            <p>
                Attendance records are automatically created when employees check in/out using biometric devices.
                You can also create manual entries if needed.
            </p>
        </field>
    </record>

    <!-- Daily Attendance Action -->
    <record id="action_biometric_attendance_daily" model="ir.actions.act_window">
        <field name="name">Daily Attendance</field>
        <field name="res_model">biometric.attendance</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_today': 1, 'search_default_group_employee': 1}</field>
        <field name="domain">[('attendance_type', '=', 'check_in')]</field>
    </record>
</odoo>
