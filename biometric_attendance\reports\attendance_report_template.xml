<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Attendance Report Template -->
    <template id="attendance_report_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="oe_structure"/>
                        
                        <!-- Report Header -->
                        <div class="row">
                            <div class="col-12">
                                <h2 class="text-center">
                                    <strong>Attendance Report</strong>
                                </h2>
                                <div class="text-center">
                                    <p>
                                        Period: <span t-esc="doc.date_from"/> to <span t-esc="doc.date_to"/>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Report Summary -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h4>Summary</h4>
                                <table class="table table-sm table-bordered">
                                    <tr>
                                        <td><strong>Total Employees:</strong></td>
                                        <td t-esc="doc.summary.get('total_employees', 0)"/>
                                        <td><strong>Total Present:</strong></td>
                                        <td t-esc="doc.summary.get('total_present', 0)"/>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Late:</strong></td>
                                        <td t-esc="doc.summary.get('total_late', 0)"/>
                                        <td><strong>Total Absent:</strong></td>
                                        <td t-esc="doc.summary.get('total_absent', 0)"/>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Working Hours:</strong></td>
                                        <td t-esc="'%.2f' % doc.summary.get('total_working_hours', 0)"/>
                                        <td><strong>Total Overtime:</strong></td>
                                        <td t-esc="'%.2f' % doc.summary.get('total_overtime_hours', 0)"/>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Employee Details -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h4>Employee Details</h4>
                                <t t-foreach="doc.employees" t-as="emp_data">
                                    <div class="mt-3">
                                        <h5>
                                            <span t-esc="emp_data['employee'].name"/>
                                            (<span t-esc="emp_data['employee'].employee_code"/>)
                                        </h5>
                                        
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Check In</th>
                                                    <th>Check Out</th>
                                                    <th>Status</th>
                                                    <th>Late (min)</th>
                                                    <th>Hours</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="emp_data['records']" t-as="record">
                                                    <tr>
                                                        <td t-esc="record.get('date', '')"/>
                                                        <td t-esc="record.get('check_in', '') or '-'"/>
                                                        <td t-esc="record.get('check_out', '') or '-'"/>
                                                        <td>
                                                            <span t-att-class="'badge badge-' + ('success' if record.get('status') == 'present' else 'warning' if record.get('status') == 'late' else 'danger')">
                                                                <t t-esc="record.get('status', '').title()"/>
                                                            </span>
                                                        </td>
                                                        <td t-esc="record.get('late_minutes', 0)"/>
                                                        <td t-esc="'%.2f' % record.get('worked_hours', 0)"/>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                            </div>
                        </div>
                        
                        <div class="oe_structure"/>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- Employee Summary Template -->
    <template id="employee_summary_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="oe_structure"/>
                        
                        <div class="row">
                            <div class="col-12">
                                <h2 class="text-center">
                                    <strong>Employee Summary Report</strong>
                                </h2>
                                <div class="text-center">
                                    <h4 t-esc="doc.name"/> (<span t-esc="doc.employee_code"/>)
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-6">
                                <h5>Employee Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Department:</strong></td>
                                        <td t-esc="doc.department or '-'"/>
                                    </tr>
                                    <tr>
                                        <td><strong>Position:</strong></td>
                                        <td t-esc="doc.position or '-'"/>
                                    </tr>
                                    <tr>
                                        <td><strong>Schedule:</strong></td>
                                        <td t-esc="doc.schedule_id.name if doc.schedule_id else '-'"/>
                                    </tr>
                                    <tr>
                                        <td><strong>Hire Date:</strong></td>
                                        <td t-esc="doc.hire_date or '-'"/>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-6">
                                <h5>Attendance Statistics</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Total Attendances:</strong></td>
                                        <td t-esc="doc.total_attendances"/>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Delays:</strong></td>
                                        <td t-esc="doc.total_delays"/>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Absences:</strong></td>
                                        <td t-esc="doc.total_absences"/>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="oe_structure"/>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- Device Status Template -->
    <template id="device_status_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="oe_structure"/>
                        
                        <div class="row">
                            <div class="col-12">
                                <h2 class="text-center">
                                    <strong>Device Status Report</strong>
                                </h2>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Device Name</th>
                                            <th>IP Address</th>
                                            <th>Type</th>
                                            <th>Model</th>
                                            <th>Status</th>
                                            <th>Last Sync</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td t-esc="doc.name"/>
                                            <td t-esc="doc.ip_address"/>
                                            <td t-esc="doc.device_type"/>
                                            <td t-esc="doc.model or '-'"/>
                                            <td>
                                                <span t-att-class="'badge badge-' + ('success' if doc.is_connected else 'danger')">
                                                    <t t-if="doc.is_connected">Connected</t>
                                                    <t t-else="">Disconnected</t>
                                                </span>
                                            </td>
                                            <td t-esc="doc.last_sync_date or '-'"/>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="oe_structure"/>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
