# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import socket
import struct
from datetime import datetime
import logging
from ..lib.zkteco_client import ZKTecoClient

_logger = logging.getLogger(__name__)


class BiometricDevice(models.Model):
    _name = 'biometric.device'
    _description = 'Biometric Device'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(string='Device Name', required=True, tracking=True)
    ip_address = fields.Char(string='IP Address', required=True, tracking=True)
    port = fields.Integer(string='Port', default=4370, required=True, tracking=True)
    
    # Device Information
    device_type = fields.Selection([
        ('zkteco', 'ZKTeco'),
        ('other', 'Other'),
    ], string='Device Type', default='zkteco', required=True)
    
    model = fields.Char(string='Device Model')
    serial_number = fields.Char(string='Serial Number')
    firmware_version = fields.Char(string='Firmware Version')
    
    # Connection Settings
    timeout = fields.Integer(string='Connection Timeout (seconds)', default=30)
    password = fields.Char(string='Device Password')
    
    # Status
    active = fields.Boolean(string='Active', default=True)
    is_connected = fields.Boolean(string='Connected', default=False, readonly=True)
    last_sync_date = fields.Datetime(string='Last Sync Date', readonly=True)
    last_connection_test = fields.Datetime(string='Last Connection Test', readonly=True)
    
    # Statistics
    total_users = fields.Integer(string='Total Users', readonly=True)
    total_records = fields.Integer(string='Total Records', readonly=True)
    
    # Relations
    attendance_ids = fields.One2many('biometric.attendance', 'device_id', string='Attendances')
    
    @api.constrains('ip_address')
    def _check_ip_address(self):
        for device in self:
            if device.ip_address:
                # Basic IP validation
                parts = device.ip_address.split('.')
                if len(parts) != 4:
                    raise ValidationError(_('Invalid IP address format!'))
                try:
                    for part in parts:
                        if not 0 <= int(part) <= 255:
                            raise ValidationError(_('Invalid IP address!'))
                except ValueError:
                    raise ValidationError(_('Invalid IP address!'))
    
    @api.constrains('port')
    def _check_port(self):
        for device in self:
            if not 1 <= device.port <= 65535:
                raise ValidationError(_('Port must be between 1 and 65535!'))
    
    def test_connection(self):
        """Test connection to the device"""
        self.ensure_one()
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((self.ip_address, self.port))
            sock.close()
            
            if result == 0:
                self.write({
                    'is_connected': True,
                    'last_connection_test': fields.Datetime.now()
                })
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Connection successful!'),
                        'type': 'success',
                    }
                }
            else:
                self.write({
                    'is_connected': False,
                    'last_connection_test': fields.Datetime.now()
                })
                raise UserError(_('Connection failed! Please check IP address and port.'))
                
        except Exception as e:
            self.write({
                'is_connected': False,
                'last_connection_test': fields.Datetime.now()
            })
            raise UserError(_('Connection error: %s') % str(e))
    
    def sync_employees(self):
        """Sync employees from device"""
        self.ensure_one()
        if not self.is_connected:
            self.test_connection()
        
        try:
            # This is a placeholder for actual device communication
            # In real implementation, you would use the device's SDK or protocol
            employees_data = self._get_employees_from_device()
            
            created_count = 0
            updated_count = 0
            
            for emp_data in employees_data:
                existing_employee = self.env['biometric.employee'].search([
                    ('biometric_id', '=', emp_data['biometric_id'])
                ], limit=1)
                
                if existing_employee:
                    existing_employee.write({
                        'name': emp_data.get('name', existing_employee.name),
                        'card_number': emp_data.get('card_number'),
                        'is_synced': True,
                        'last_sync_date': fields.Datetime.now()
                    })
                    updated_count += 1
                else:
                    self.env['biometric.employee'].create({
                        'name': emp_data['name'],
                        'biometric_id': emp_data['biometric_id'],
                        'card_number': emp_data.get('card_number'),
                        'employee_code': emp_data.get('employee_code', emp_data['biometric_id']),
                        'is_synced': True,
                        'last_sync_date': fields.Datetime.now()
                    })
                    created_count += 1
            
            self.write({'last_sync_date': fields.Datetime.now()})
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Complete'),
                    'message': _('Created: %d, Updated: %d employees') % (created_count, updated_count),
                    'type': 'success',
                }
            }
            
        except Exception as e:
            _logger.error("Error syncing employees: %s", str(e))
            raise UserError(_('Sync failed: %s') % str(e))
    
    def sync_attendance(self, date_from=None, date_to=None):
        """Sync attendance records from device"""
        self.ensure_one()
        if not self.is_connected:
            self.test_connection()
        
        try:
            # This is a placeholder for actual device communication
            attendance_data = self._get_attendance_from_device(date_from, date_to)
            
            created_count = 0
            
            for att_data in attendance_data:
                # Check if record already exists
                existing = self.env['biometric.attendance'].search([
                    ('device_id', '=', self.id),
                    ('device_user_id', '=', att_data['user_id']),
                    ('check_time', '=', att_data['check_time'])
                ])
                
                if not existing:
                    # Find employee by biometric_id
                    employee = self.env['biometric.employee'].search([
                        ('biometric_id', '=', att_data['user_id'])
                    ], limit=1)
                    
                    if employee:
                        self.env['biometric.attendance'].create({
                            'employee_id': employee.id,
                            'attendance_date': att_data['check_time'].date(),
                            'check_time': att_data['check_time'],
                            'attendance_type': att_data.get('type', 'check_in'),
                            'device_id': self.id,
                            'device_user_id': att_data['user_id'],
                            'verification_type': att_data.get('verification_type', 'fingerprint'),
                            'is_synced': True,
                        })
                        created_count += 1
            
            self.write({'last_sync_date': fields.Datetime.now()})
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Complete'),
                    'message': _('Created %d attendance records') % created_count,
                    'type': 'success',
                }
            }
            
        except Exception as e:
            _logger.error("Error syncing attendance: %s", str(e))
            raise UserError(_('Sync failed: %s') % str(e))
    
    def _get_employees_from_device(self):
        """Get employees data from device"""
        if self.device_type != 'zkteco':
            return []

        try:
            client = ZKTecoClient(self.ip_address, self.port, self.timeout, self.password or 0)
            if client.connect():
                users = client.get_users()
                client.disconnect()
                return users
            return []
        except Exception as e:
            _logger.error("Error getting employees from device: %s", str(e))
            return []

    def _get_attendance_from_device(self, date_from=None, date_to=None):
        """Get attendance data from device"""
        if self.device_type != 'zkteco':
            return []

        try:
            client = ZKTecoClient(self.ip_address, self.port, self.timeout, self.password or 0)
            if client.connect():
                records = client.get_attendance()
                client.disconnect()

                # Filter by date if specified
                if date_from or date_to:
                    filtered_records = []
                    for record in records:
                        record_date = record['check_time'].date()
                        if date_from and record_date < date_from:
                            continue
                        if date_to and record_date > date_to:
                            continue
                        filtered_records.append(record)
                    return filtered_records

                return records
            return []
        except Exception as e:
            _logger.error("Error getting attendance from device: %s", str(e))
            return []
    
    def action_view_attendances(self):
        """View attendances from this device"""
        self.ensure_one()
        return {
            'name': _('Device Attendances'),
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.attendance',
            'view_mode': 'tree,form',
            'domain': [('device_id', '=', self.id)],
            'context': {'default_device_id': self.id},
        }
