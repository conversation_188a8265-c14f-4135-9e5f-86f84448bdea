# دليل تثبيت موديول Biometric Attendance

## 🚀 تعليمات التثبيت السريع

### 1. نسخ الموديول
```bash
# انسخ مجلد biometric_attendance إلى مجلد addons في Odoo
cp -r biometric_attendance /path/to/odoo/addons/
```

### 2. إعادة تشغيل Odoo
```bash
# أعد تشغيل خادم Odoo
sudo systemctl restart odoo
# أو
python3 odoo-bin -c /path/to/odoo.conf
```

### 3. تحديث قائمة التطبيقات
- اذهب إلى Apps في Odoo
- اضغط على "Update Apps List"
- ابحث عن "Biometric Attendance Management"

### 4. تثبيت الموديول
- اضغط على "Install" للموديول

## ⚙️ الإعداد الأولي

### 1. إن<PERSON>اء جدول عمل افتراضي
```
Biometric Attendance → Configuration → Work Schedules
- اسم الجدول: "Standard Schedule"
- كود الجدول: "DEFAULT"
- ساعات العمل: 8:00 - 17:00
- فترة السماح: 15 دقيقة
```

### 2. إضافة جهاز البصمة
```
Biometric Attendance → Devices → Biometric Devices
- اسم الجهاز: "Main Entrance"
- عنوان IP: *************
- المنفذ: 4370
- نوع الجهاز: ZKTeco
```

### 3. إضافة الموظفين
```
Biometric Attendance → Employees → Employees
- اسم الموظف: "أحمد محمد"
- كود الموظف: "EMP001"
- معرف البصمة: "1001"
- جدول العمل: "Standard Schedule"
```

### 4. اختبار الاتصال
- اذهب إلى الجهاز واضغط "Test Connection"
- إذا نجح الاتصال، اضغط "Sync Employees"

## 🔧 إعدادات متقدمة

### صلاحيات المستخدمين
```
Settings → Users & Companies → Users
- Biometric User: عرض فقط
- Attendance Supervisor: إدارة الحضور
- Biometric Administrator: إدارة كاملة
```

### المهام المجدولة
```
Settings → Technical → Automation → Scheduled Actions
- Auto Sync Attendance: كل ساعة
- Process Daily Attendance: يومياً
- Test Device Connections: كل 30 دقيقة
```

## 📊 استخدام التقارير

### تقرير الحضور
```
Biometric Attendance → Reports → Attendance Report
- اختر نوع التقرير (يومي/أسبوعي/شهري)
- حدد الفترة الزمنية
- اختر الموظفين
- اختر تنسيق الإخراج (PDF/Excel)
```

### ملخص الموظف
```
Biometric Attendance → Reports → Employee Summary
- اختر الموظف
- حدد الفترة الزمنية
- اعرض الملخص
```

## 🔍 استكشاف الأخطاء

### مشكلة: فشل الاتصال بالجهاز
**الحل:**
1. تحقق من عنوان IP والمنفذ
2. تأكد من تشغيل الجهاز
3. تحقق من إعدادات الشبكة
4. تأكد من تفعيل TCP/IP في الجهاز

### مشكلة: لا يتم مزامنة الحضور
**الحل:**
1. تحقق من حالة الاتصال بالجهاز
2. تأكد من تشغيل المهام المجدولة
3. تحقق من سجلات النظام
4. تأكد من صحة معرفات البصمة

### مشكلة: الموظف غير موجود
**الحل:**
1. تأكد من تطابق معرف البصمة
2. تحقق من أن الموظف نشط
3. تأكد من تعيين جدول عمل للموظف

## 📞 الدعم الفني

للحصول على المساعدة:
- البريد الإلكتروني: <EMAIL>
- الوثائق: https://docs.yourcompany.com
- GitHub: https://github.com/yourcompany/biometric-attendance

## 🎯 نصائح مهمة

1. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من البيانات قبل التحديث
2. **الاختبار**: اختبر الموديول في بيئة تطوير أولاً
3. **المراقبة**: راقب سجلات النظام بانتظام
4. **التحديث**: حدث الموديول بانتظام للحصول على أحدث الميزات

## ✅ قائمة التحقق

- [ ] تم نسخ الموديول إلى مجلد addons
- [ ] تم إعادة تشغيل Odoo
- [ ] تم تحديث قائمة التطبيقات
- [ ] تم تثبيت الموديول
- [ ] تم إنشاء جدول العمل الافتراضي
- [ ] تم إضافة جهاز البصمة
- [ ] تم اختبار الاتصال بالجهاز
- [ ] تم إضافة الموظفين
- [ ] تم مزامنة البيانات
- [ ] تم اختبار التقارير
- [ ] تم تعيين الصلاحيات

🎉 **تهانينا! تم تثبيت وإعداد موديول Biometric Attendance بنجاح!**
