<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Simple Biometric Attendance Cron Jobs -->
    
    <!-- Auto Sync Attendance Data -->
    <record id="cron_auto_sync_attendance" model="ir.cron">
        <field name="name">Auto Sync Biometric Attendance</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code">pass</field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Process Daily Attendance -->
    <record id="cron_process_daily_attendance" model="ir.cron">
        <field name="name">Process Daily Attendance</field>
        <field name="model_id" ref="model_biometric_attendance_processor"/>
        <field name="state">code</field>
        <field name="code">pass</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="doall" eval="False"/>
    </record>

    <!-- Test Device Connections -->
    <record id="cron_test_device_connections" model="ir.cron">
        <field name="name">Test Biometric Device Connections</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code">pass</field>
        <field name="interval_number">30</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="doall" eval="False"/>
    </record>

</odoo>
