<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Report Wizard Form View -->
    <record id="view_biometric_attendance_report_wizard_form" model="ir.ui.view">
        <field name="name">biometric.attendance.report.wizard.form</field>
        <field name="model">biometric.attendance.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Attendance Report">
                <sheet>
                    <group>
                        <group name="report_settings" string="Report Settings">
                            <field name="report_type" widget="radio"/>
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="output_format" widget="radio"/>
                        </group>
                        <group name="filters" string="Filters">
                            <field name="employee_ids" widget="many2many_tags"/>
                            <field name="department"/>
                            <field name="schedule_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="options" string="Report Options">
                            <field name="include_absent"/>
                            <field name="include_holidays"/>
                            <field name="group_by_employee"/>
                            <field name="show_details"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button name="action_generate_report" type="object" string="Generate Report" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Report Wizard Action -->
    <record id="action_biometric_attendance_report_wizard" model="ir.actions.act_window">
        <field name="name">Attendance Report</field>
        <field name="res_model">biometric.attendance.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Employee Summary Wizard Form View -->
    <record id="view_biometric_employee_summary_wizard_form" model="ir.ui.view">
        <field name="name">biometric.employee.summary.wizard.form</field>
        <field name="model">biometric.employee.summary.wizard</field>
        <field name="arch" type="xml">
            <form string="Employee Summary">
                <sheet>
                    <group>
                        <field name="employee_id" required="1"/>
                        <field name="date_from"/>
                        <field name="date_to"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_view_summary" type="object" string="View Summary" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Employee Summary Wizard Action -->
    <record id="action_biometric_employee_summary_wizard" model="ir.actions.act_window">
        <field name="name">Employee Summary</field>
        <field name="res_model">biometric.employee.summary.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Sync Wizard Form View -->
    <record id="view_biometric_sync_wizard_form" model="ir.ui.view">
        <field name="name">biometric.sync.wizard.form</field>
        <field name="model">biometric.sync.wizard</field>
        <field name="arch" type="xml">
            <form string="Device Sync">
                <sheet>
                    <group>
                        <group name="basic_settings" string="Sync Settings">
                            <field name="device_id" required="1"/>
                            <field name="sync_type" widget="radio"/>
                        </group>
                        <group name="attendance_settings" string="Attendance Settings" 
                               attrs="{'invisible': [('sync_type', '=', 'employees')]}">
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="options" string="Options">
                            <field name="overwrite_existing"/>
                            <field name="create_missing_employees"/>
                        </group>
                    </group>
                    
                    <group attrs="{'invisible': [('sync_results', '=', False)]}">
                        <field name="sync_results" readonly="1" nolabel="1"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_sync" type="object" string="Start Sync" class="btn-primary" 
                            attrs="{'invisible': [('sync_results', '!=', False)]}"/>
                    <button name="action_close" type="object" string="Close" class="btn-secondary" 
                            attrs="{'invisible': [('sync_results', '=', False)]}"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" 
                            attrs="{'invisible': [('sync_results', '!=', False)]}"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Sync Wizard Action -->
    <record id="action_biometric_sync_wizard" model="ir.actions.act_window">
        <field name="name">Device Sync</field>
        <field name="res_model">biometric.sync.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Process Attendance Wizard Form View -->
    <record id="view_biometric_process_attendance_wizard_form" model="ir.ui.view">
        <field name="name">biometric.process.attendance.wizard.form</field>
        <field name="model">biometric.process.attendance.wizard</field>
        <field name="arch" type="xml">
            <form string="Process Attendance">
                <sheet>
                    <group>
                        <group name="date_settings" string="Date Range">
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                        <group name="employee_settings" string="Employees">
                            <field name="employee_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="options" string="Processing Options">
                            <field name="recalculate_existing"/>
                            <field name="create_absence_records"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button name="action_process" type="object" string="Process" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Process Attendance Wizard Action -->
    <record id="action_biometric_process_attendance_wizard" model="ir.actions.act_window">
        <field name="name">Process Attendance</field>
        <field name="res_model">biometric.process.attendance.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Add menu items for wizards -->
    <menuitem id="menu_biometric_attendance_report" 
              name="Attendance Report" 
              parent="menu_biometric_reports" 
              action="action_biometric_attendance_report_wizard" 
              sequence="10"/>

    <menuitem id="menu_biometric_employee_summary" 
              name="Employee Summary" 
              parent="menu_biometric_reports" 
              action="action_biometric_employee_summary_wizard" 
              sequence="20"/>

    <menuitem id="menu_biometric_sync_device" 
              name="Sync Device" 
              parent="menu_biometric_devices" 
              action="action_biometric_sync_wizard" 
              sequence="20"/>

    <menuitem id="menu_biometric_process_attendance" 
              name="Process Attendance" 
              parent="menu_biometric_configuration" 
              action="action_biometric_process_attendance_wizard" 
              sequence="30"/>
</odoo>
