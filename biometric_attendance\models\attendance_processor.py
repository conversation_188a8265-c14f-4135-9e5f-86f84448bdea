# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta, date
import logging

_logger = logging.getLogger(__name__)


class AttendanceProcessor(models.Model):
    _name = 'biometric.attendance.processor'
    _description = 'Attendance Processing Engine'

    @api.model
    def process_daily_attendance(self, target_date=None):
        """Process attendance for a specific date"""
        if not target_date:
            target_date = date.today()
        
        # Get all employees with schedules
        employees = self.env['biometric.employee'].search([
            ('active', '=', True),
            ('schedule_id', '!=', False)
        ])
        
        processed_count = 0
        for employee in employees:
            try:
                self._process_employee_attendance(employee, target_date)
                processed_count += 1
            except Exception as e:
                _logger.error(f"Error processing attendance for employee {employee.name}: {str(e)}")
        
        _logger.info(f"Processed attendance for {processed_count} employees on {target_date}")
        return processed_count
    
    def _process_employee_attendance(self, employee, target_date):
        """Process attendance for a specific employee and date"""
        # Check if it's a working day
        if not self._is_working_day(employee, target_date):
            return
        
        # Get attendance records for the day
        attendances = self.env['biometric.attendance'].search([
            ('employee_id', '=', employee.id),
            ('attendance_date', '=', target_date)
        ]).sorted('check_time')
        
        if not attendances:
            # Create absence record
            self._create_absence_record(employee, target_date)
            return
        
        # Process attendance records
        self._analyze_attendance_records(employee, target_date, attendances)
    
    def _is_working_day(self, employee, target_date):
        """Check if the date is a working day for the employee"""
        # Check if it's a holiday
        holiday = self.env['biometric.holiday'].search([
            ('date', '=', target_date),
            ('active', '=', True)
        ], limit=1)
        if holiday:
            return False
        
        # Check schedule
        if not employee.schedule_id:
            return False
        
        weekday = target_date.weekday()
        schedule_line = employee.schedule_id.line_ids.filtered(
            lambda l: l.dayofweek == str(weekday) and l.is_working_day
        )
        
        return bool(schedule_line)
    
    def _create_absence_record(self, employee, target_date):
        """Create absence record for employee"""
        existing = self.env['biometric.attendance'].search([
            ('employee_id', '=', employee.id),
            ('attendance_date', '=', target_date),
            ('status', '=', 'absent')
        ])
        
        if not existing:
            # Get scheduled times
            schedule_line = self._get_schedule_line(employee, target_date)
            if schedule_line:
                scheduled_in = self._get_datetime_from_float(target_date, schedule_line.hour_from)
                
                self.env['biometric.attendance'].create({
                    'employee_id': employee.id,
                    'attendance_date': target_date,
                    'check_time': scheduled_in,
                    'attendance_type': 'check_in',
                    'status': 'absent',
                    'is_manual': True,
                    'is_synced': False,
                    'notes': 'Auto-generated absence record'
                })
    
    def _analyze_attendance_records(self, employee, target_date, attendances):
        """Analyze attendance records and calculate status"""
        schedule_line = self._get_schedule_line(employee, target_date)
        if not schedule_line:
            return
        
        # Get check-in and check-out records
        check_ins = attendances.filtered(lambda a: a.attendance_type == 'check_in')
        check_outs = attendances.filtered(lambda a: a.attendance_type == 'check_out')
        
        if not check_ins:
            self._create_absence_record(employee, target_date)
            return
        
        # Get first check-in and last check-out
        first_check_in = check_ins[0]
        last_check_out = check_outs[-1] if check_outs else None
        
        # Calculate scheduled times
        scheduled_in = self._get_datetime_from_float(target_date, schedule_line.hour_from)
        scheduled_out = self._get_datetime_from_float(target_date, schedule_line.hour_to)
        
        # Update attendance status
        self._update_attendance_status(first_check_in, scheduled_in, scheduled_out, last_check_out)
    
    def _get_schedule_line(self, employee, target_date):
        """Get schedule line for employee and date"""
        if not employee.schedule_id:
            return None
        
        weekday = target_date.weekday()
        return employee.schedule_id.line_ids.filtered(
            lambda l: l.dayofweek == str(weekday) and l.is_working_day
        )[:1]
    
    def _get_datetime_from_float(self, target_date, time_float):
        """Convert float time to datetime"""
        hours = int(time_float)
        minutes = int((time_float - hours) * 60)
        return datetime.combine(target_date, datetime.min.time().replace(hour=hours, minute=minutes))
    
    def _update_attendance_status(self, check_in_record, scheduled_in, scheduled_out, check_out_record):
        """Update attendance record status"""
        # Calculate lateness
        is_late = check_in_record.check_time > scheduled_in
        late_minutes = 0
        
        if is_late:
            delta = check_in_record.check_time - scheduled_in
            late_minutes = int(delta.total_seconds() / 60)
            
            # Apply grace period
            grace_period = check_in_record.employee_id.schedule_id.grace_period_minutes or 0
            if late_minutes <= grace_period:
                is_late = False
                late_minutes = 0
        
        # Determine status
        status = 'present'
        if is_late:
            status = 'late'
        
        # Check for early leave
        if check_out_record and check_out_record.check_time < scheduled_out:
            status = 'early_leave'
        
        # Update the record
        check_in_record.write({
            'status': status,
            'is_late': is_late,
            'late_minutes': late_minutes,
        })
    
    @api.model
    def calculate_working_hours(self, employee_id, date_from, date_to):
        """Calculate working hours for employee in date range"""
        attendances = self.env['biometric.attendance'].search([
            ('employee_id', '=', employee_id),
            ('attendance_date', '>=', date_from),
            ('attendance_date', '<=', date_to),
            ('status', '!=', 'absent')
        ])
        
        total_hours = 0
        total_overtime = 0
        total_late_minutes = 0
        days_present = 0
        days_late = 0
        
        # Group by date
        dates = list(set(attendances.mapped('attendance_date')))
        
        for att_date in dates:
            day_attendances = attendances.filtered(lambda a: a.attendance_date == att_date)
            check_ins = day_attendances.filtered(lambda a: a.attendance_type == 'check_in')
            check_outs = day_attendances.filtered(lambda a: a.attendance_type == 'check_out')
            
            if check_ins and check_outs:
                first_in = min(check_ins.mapped('check_time'))
                last_out = max(check_outs.mapped('check_time'))
                
                # Calculate working hours
                delta = last_out - first_in
                day_hours = delta.total_seconds() / 3600
                total_hours += day_hours
                
                # Calculate overtime
                employee = self.env['biometric.employee'].browse(employee_id)
                if employee.schedule_id:
                    overtime_threshold = employee.schedule_id.overtime_threshold
                    if day_hours > overtime_threshold:
                        total_overtime += (day_hours - overtime_threshold)
                
                days_present += 1
                
                # Check for lateness
                if any(check_ins.mapped('is_late')):
                    days_late += 1
                    total_late_minutes += sum(check_ins.mapped('late_minutes'))
        
        return {
            'total_hours': round(total_hours, 2),
            'total_overtime': round(total_overtime, 2),
            'total_late_minutes': total_late_minutes,
            'days_present': days_present,
            'days_late': days_late,
            'average_hours_per_day': round(total_hours / max(days_present, 1), 2)
        }
    
    @api.model
    def generate_attendance_summary(self, employee_id, date_from, date_to):
        """Generate attendance summary for employee"""
        employee = self.env['biometric.employee'].browse(employee_id)
        if not employee.exists():
            return {}
        
        # Calculate working hours
        hours_data = self.calculate_working_hours(employee_id, date_from, date_to)
        
        # Get attendance records
        attendances = self.env['biometric.attendance'].search([
            ('employee_id', '=', employee_id),
            ('attendance_date', '>=', date_from),
            ('attendance_date', '<=', date_to)
        ])
        
        # Count by status
        status_counts = {}
        for status in ['present', 'late', 'absent', 'early_leave']:
            status_counts[status] = len(attendances.filtered(lambda a: a.status == status))
        
        # Calculate expected working days
        expected_days = self._calculate_expected_working_days(employee, date_from, date_to)
        
        return {
            'employee': employee,
            'date_from': date_from,
            'date_to': date_to,
            'expected_working_days': expected_days,
            'status_counts': status_counts,
            'hours_data': hours_data,
            'attendance_rate': round((status_counts['present'] + status_counts['late']) / max(expected_days, 1) * 100, 2)
        }
    
    def _calculate_expected_working_days(self, employee, date_from, date_to):
        """Calculate expected working days in date range"""
        if not employee.schedule_id:
            return 0
        
        working_weekdays = employee.schedule_id.line_ids.filtered('is_working_day').mapped('dayofweek')
        working_weekdays = [int(d) for d in working_weekdays]
        
        current_date = date_from
        working_days = 0
        
        while current_date <= date_to:
            # Check if it's a working weekday
            if current_date.weekday() in working_weekdays:
                # Check if it's not a holiday
                holiday = self.env['biometric.holiday'].search([
                    ('date', '=', current_date),
                    ('active', '=', True)
                ], limit=1)
                if not holiday:
                    working_days += 1
            
            current_date += timedelta(days=1)
        
        return working_days
