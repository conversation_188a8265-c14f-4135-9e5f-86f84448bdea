odoo.define('biometric_attendance.dashboard', function (require) {
'use strict';

var AbstractAction = require('web.AbstractAction');
var core = require('web.core');
var rpc = require('web.rpc');
var session = require('web.session');

var QWeb = core.qweb;
var _t = core._t;

var BiometricDashboard = AbstractAction.extend({
    template: 'BiometricAttendanceDashboard',
    
    events: {
        'click .btn_manual_checkin': '_onManualCheckIn',
        'click .btn_manual_checkout': '_onManualCheckOut',
        'click .btn_refresh_status': '_onRefreshStatus',
        'click .btn_view_attendance': '_onViewAttendance',
    },
    
    init: function(parent, context) {
        this._super(parent, context);
        this.employee_data = {};
        this.attendance_status = {};
    },
    
    willStart: function() {
        var self = this;
        return this._super().then(function() {
            return self._loadData();
        });
    },
    
    start: function() {
        var self = this;
        return this._super().then(function() {
            self._renderDashboard();
            self._startAutoRefresh();
        });
    },
    
    _loadData: function() {
        var self = this;
        return rpc.query({
            route: '/biometric/attendance/api/status',
            params: {
                employee_id: session.uid, // This should be mapped to employee
            }
        }).then(function(result) {
            if (result.success) {
                self.attendance_status = result.data;
            }
        });
    },
    
    _renderDashboard: function() {
        var self = this;
        this.$('.attendance_status_container').html(
            QWeb.render('BiometricAttendanceStatus', {
                status: self.attendance_status
            })
        );
    },
    
    _startAutoRefresh: function() {
        var self = this;
        setInterval(function() {
            self._loadData().then(function() {
                self._renderDashboard();
            });
        }, 30000); // Refresh every 30 seconds
    },
    
    _onManualCheckIn: function(ev) {
        ev.preventDefault();
        var self = this;
        
        rpc.query({
            route: '/biometric/attendance/api/checkin',
            params: {
                employee_id: this.attendance_status.employee_id,
            }
        }).then(function(result) {
            if (result.success) {
                self._showNotification(_t('Check-in successful'), 'success');
                self._loadData().then(function() {
                    self._renderDashboard();
                });
            } else {
                self._showNotification(result.message, 'danger');
            }
        });
    },
    
    _onManualCheckOut: function(ev) {
        ev.preventDefault();
        var self = this;
        
        rpc.query({
            route: '/biometric/attendance/api/checkout',
            params: {
                employee_id: this.attendance_status.employee_id,
            }
        }).then(function(result) {
            if (result.success) {
                self._showNotification(_t('Check-out successful'), 'success');
                self._loadData().then(function() {
                    self._renderDashboard();
                });
            } else {
                self._showNotification(result.message, 'danger');
            }
        });
    },
    
    _onRefreshStatus: function(ev) {
        ev.preventDefault();
        var self = this;
        
        this.$('.btn_refresh_status').addClass('disabled').find('i').addClass('fa-spin');
        
        this._loadData().then(function() {
            self._renderDashboard();
            self.$('.btn_refresh_status').removeClass('disabled').find('i').removeClass('fa-spin');
        });
    },
    
    _onViewAttendance: function(ev) {
        ev.preventDefault();
        
        this.do_action({
            name: _t('My Attendance'),
            type: 'ir.actions.act_window',
            res_model: 'biometric.attendance',
            view_mode: 'tree,form',
            domain: [['employee_id', '=', this.attendance_status.employee_id]],
            context: {
                'search_default_this_month': 1,
            },
        });
    },
    
    _showNotification: function(message, type) {
        this.displayNotification({
            title: type === 'success' ? _t('Success') : _t('Error'),
            message: message,
            type: type,
        });
    },
});

core.action_registry.add('biometric_attendance_dashboard', BiometricDashboard);

return BiometricDashboard;

});

// Device sync functionality
odoo.define('biometric_attendance.device_sync', function (require) {
'use strict';

var core = require('web.core');
var rpc = require('web.rpc');
var Dialog = require('web.Dialog');

var _t = core._t;

function syncDevice(deviceId, syncType) {
    return rpc.query({
        model: 'biometric.device',
        method: 'sync_' + syncType,
        args: [deviceId],
    });
}

function testDeviceConnection(deviceId) {
    return rpc.query({
        model: 'biometric.device',
        method: 'test_connection',
        args: [deviceId],
    });
}

// Export functions for use in other modules
return {
    syncDevice: syncDevice,
    testDeviceConnection: testDeviceConnection,
};

});

// Attendance processing utilities
odoo.define('biometric_attendance.utils', function (require) {
'use strict';

var core = require('web.core');
var time = require('web.time');

var _t = core._t;

function formatTime(timeFloat) {
    var hours = Math.floor(timeFloat);
    var minutes = Math.round((timeFloat - hours) * 60);
    return String(hours).padStart(2, '0') + ':' + String(minutes).padStart(2, '0');
}

function formatDuration(hours) {
    if (hours < 1) {
        return Math.round(hours * 60) + ' ' + _t('minutes');
    } else {
        return hours.toFixed(1) + ' ' + _t('hours');
    }
}

function getStatusColor(status) {
    var colors = {
        'present': '#28a745',
        'late': '#ffc107',
        'absent': '#dc3545',
        'early_leave': '#17a2b8',
    };
    return colors[status] || '#6c757d';
}

function getStatusIcon(status) {
    var icons = {
        'present': 'fa-check-circle',
        'late': 'fa-clock-o',
        'absent': 'fa-times-circle',
        'early_leave': 'fa-sign-out',
    };
    return icons[status] || 'fa-question-circle';
}

// Export utility functions
return {
    formatTime: formatTime,
    formatDuration: formatDuration,
    getStatusColor: getStatusColor,
    getStatusIcon: getStatusIcon,
};

});

// Real-time updates for attendance
odoo.define('biometric_attendance.realtime', function (require) {
'use strict';

var core = require('web.core');
var session = require('web.session');

var bus = core.bus;

// Listen for attendance updates
bus.on('biometric_attendance_update', null, function(data) {
    if (data.employee_id && data.employee_id === session.employee_id) {
        // Refresh current view if it's attendance related
        var currentController = core.action_manager.getCurrentController();
        if (currentController && currentController.modelName === 'biometric.attendance') {
            currentController.reload();
        }
        
        // Show notification
        core.action_manager.displayNotification({
            title: core._t('Attendance Update'),
            message: data.message,
            type: 'info',
        });
    }
});

});

// Initialize when DOM is ready
$(document).ready(function() {
    
    // Auto-refresh attendance tables every 5 minutes
    if ($('.o_list_view[data-model="biometric.attendance"]').length) {
        setInterval(function() {
            if (odoo.action_manager && odoo.action_manager.getCurrentController) {
                var controller = odoo.action_manager.getCurrentController();
                if (controller && controller.modelName === 'biometric.attendance') {
                    controller.reload();
                }
            }
        }, 300000); // 5 minutes
    }
    
    // Add tooltips to status indicators
    $('[data-toggle="tooltip"]').tooltip();
    
    // Enhance device connection status display
    $('.device_status').each(function() {
        var $this = $(this);
        var isConnected = $this.data('connected');
        
        if (isConnected) {
            $this.addClass('device_connected').prepend('<i class="fa fa-circle"></i> ');
        } else {
            $this.addClass('device_disconnected').prepend('<i class="fa fa-circle"></i> ');
        }
    });
    
});
