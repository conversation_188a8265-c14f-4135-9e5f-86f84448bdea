# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class BiometricSettings(models.Model):
    _name = 'biometric.settings'
    _description = 'Biometric System Settings'
    _rec_name = 'name'

    name = fields.Char(string='Setting Name', required=True)
    
    # General Settings
    auto_sync_enabled = fields.Boolean(string='Auto Sync Enabled', default=True)
    sync_interval_hours = fields.Integer(string='Sync Interval (Hours)', default=1)
    
    # Attendance Rules
    grace_period_minutes = fields.Integer(string='Default Grace Period (Minutes)', default=15)
    overtime_threshold_hours = fields.Float(string='Overtime Threshold (Hours)', default=8.0)
    
    # Working Days
    monday_working = fields.Boolean(string='Monday Working', default=True)
    tuesday_working = fields.Boolean(string='Tuesday Working', default=True)
    wednesday_working = fields.Boolean(string='Wednesday Working', default=True)
    thursday_working = fields.Boolean(string='Thursday Working', default=True)
    friday_working = fields.Boolean(string='Friday Working', default=True)
    saturday_working = fields.Boolean(string='Saturday Working', default=False)
    sunday_working = fields.Boolean(string='Sunday Working', default=False)
    
    # Default Working Hours
    default_work_start = fields.Float(string='Default Work Start Time', default=8.0)
    default_work_end = fields.Float(string='Default Work End Time', default=17.0)
    
    # Break Settings
    break_enabled = fields.Boolean(string='Break Time Enabled', default=True)
    default_break_start = fields.Float(string='Default Break Start', default=12.0)
    default_break_end = fields.Float(string='Default Break End', default=13.0)
    
    # Notification Settings
    late_notification_enabled = fields.Boolean(string='Late Notification', default=True)
    absence_notification_enabled = fields.Boolean(string='Absence Notification', default=True)
    
    # Report Settings
    default_report_period = fields.Selection([
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ], string='Default Report Period', default='monthly')
    
    # System Settings
    timezone = fields.Selection(
        selection='_get_timezone_selection',
        string='Timezone',
        default='UTC'
    )
    
    date_format = fields.Selection([
        ('%d/%m/%Y', 'DD/MM/YYYY'),
        ('%m/%d/%Y', 'MM/DD/YYYY'),
        ('%Y-%m-%d', 'YYYY-MM-DD'),
    ], string='Date Format', default='%d/%m/%Y')
    
    time_format = fields.Selection([
        ('%H:%M', '24 Hour'),
        ('%I:%M %p', '12 Hour'),
    ], string='Time Format', default='%H:%M')
    
    @api.model
    def _get_timezone_selection(self):
        """Get timezone selection list"""
        try:
            import pytz
            timezones = []
            for tz in pytz.common_timezones:
                timezones.append((tz, tz))
            return timezones
        except ImportError:
            return [('UTC', 'UTC')]
    
    @api.model
    def get_settings(self):
        """Get system settings"""
        settings = self.search([], limit=1)
        if not settings:
            settings = self.create({'name': 'Default Settings'})
        return settings
    
    def create_default_schedule(self):
        """Create default work schedule based on settings"""
        self.ensure_one()
        
        schedule_vals = {
            'name': 'Default Schedule',
            'code': 'DEFAULT',
            'description': 'Default work schedule created from system settings',
            'grace_period_minutes': self.grace_period_minutes,
            'overtime_threshold': self.overtime_threshold_hours,
        }
        
        schedule = self.env['biometric.schedule'].create(schedule_vals)
        
        # Create schedule lines for working days
        working_days = [
            ('0', self.monday_working),
            ('1', self.tuesday_working),
            ('2', self.wednesday_working),
            ('3', self.thursday_working),
            ('4', self.friday_working),
            ('5', self.saturday_working),
            ('6', self.sunday_working),
        ]
        
        for day, is_working in working_days:
            if is_working:
                line_vals = {
                    'schedule_id': schedule.id,
                    'dayofweek': day,
                    'hour_from': self.default_work_start,
                    'hour_to': self.default_work_end,
                    'is_working_day': True,
                }
                
                if self.break_enabled:
                    line_vals.update({
                        'break_hour_from': self.default_break_start,
                        'break_hour_to': self.default_break_end,
                    })
                
                self.env['biometric.schedule.line'].create(line_vals)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Default Schedule'),
            'res_model': 'biometric.schedule',
            'res_id': schedule.id,
            'view_mode': 'form',
            'target': 'current',
        }


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'
    
    # Biometric Settings
    biometric_auto_sync = fields.Boolean(
        string='Auto Sync Attendance',
        config_parameter='biometric_attendance.auto_sync_enabled',
        default=True
    )
    
    biometric_sync_interval = fields.Integer(
        string='Sync Interval (Hours)',
        config_parameter='biometric_attendance.sync_interval_hours',
        default=1
    )
    
    biometric_grace_period = fields.Integer(
        string='Grace Period (Minutes)',
        config_parameter='biometric_attendance.grace_period_minutes',
        default=15
    )
    
    biometric_overtime_threshold = fields.Float(
        string='Overtime Threshold (Hours)',
        config_parameter='biometric_attendance.overtime_threshold_hours',
        default=8.0
    )
    
    def set_values(self):
        super().set_values()
        # Update biometric settings when config is saved
        settings = self.env['biometric.settings'].get_settings()
        settings.write({
            'auto_sync_enabled': self.biometric_auto_sync,
            'sync_interval_hours': self.biometric_sync_interval,
            'grace_period_minutes': self.biometric_grace_period,
            'overtime_threshold_hours': self.biometric_overtime_threshold,
        })
    
    @api.model
    def get_values(self):
        res = super().get_values()
        settings = self.env['biometric.settings'].get_settings()
        res.update({
            'biometric_auto_sync': settings.auto_sync_enabled,
            'biometric_sync_interval': settings.sync_interval_hours,
            'biometric_grace_period': settings.grace_period_minutes,
            'biometric_overtime_threshold': settings.overtime_threshold_hours,
        })
        return res
