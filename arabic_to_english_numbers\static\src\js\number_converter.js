/**
 * Arabic to English Numbers Converter for Odoo 15
 * Simple and compatible implementation
 *
 * Converts Arabic numerals (٠١٢٣٤٥٦٧٨٩) to English numerals (0123456789)
 * in the frontend interface without affecting stored data.
 */

// Mapping of Arabic numerals to English numerals
const ARABIC_TO_ENGLISH_MAP = {
    '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
    '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
};

// Regular expression to match Arabic numerals
const ARABIC_NUMERALS_REGEX = /[٠-٩]/g;

/**
 * Convert Arabic numerals to English numerals in a text string
 * @param {string} text - Text containing Arabic numerals
 * @returns {string} Text with English numerals
 */
function convertArabicToEnglish(text) {
    if (typeof text !== 'string') {
        return text;
    }
    return text.replace(ARABIC_NUMERALS_REGEX, (match) => ARABIC_TO_ENGLISH_MAP[match] || match);
}

/**
 * Process a DOM node and convert Arabic numerals in text content
 * @param {Node} node - DOM node to process
 */
function processNode(node) {
    if (node.nodeType === Node.TEXT_NODE) {
        const originalText = node.textContent;
        const convertedText = convertArabicToEnglish(originalText);
        if (originalText !== convertedText) {
            node.textContent = convertedText;
        }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
        // Skip script and style elements
        if (node.tagName && ['SCRIPT', 'STYLE'].includes(node.tagName.toUpperCase())) {
            return;
        }
        
        // Process input values and placeholders
        if (node.tagName && ['INPUT', 'TEXTAREA'].includes(node.tagName.toUpperCase())) {
            if (node.value && typeof node.value === 'string') {
                const originalValue = node.value;
                const convertedValue = convertArabicToEnglish(originalValue);
                if (originalValue !== convertedValue) {
                    node.value = convertedValue;
                }
            }
            
            if (node.placeholder && typeof node.placeholder === 'string') {
                const originalPlaceholder = node.placeholder;
                const convertedPlaceholder = convertArabicToEnglish(originalPlaceholder);
                if (originalPlaceholder !== convertedPlaceholder) {
                    node.placeholder = convertedPlaceholder;
                }
            }
        }
        
        // Process attributes that might contain numbers
        const attributesToCheck = ['title', 'alt', 'data-original-title'];
        attributesToCheck.forEach(attr => {
            if (node.hasAttribute(attr)) {
                const originalValue = node.getAttribute(attr);
                const convertedValue = convertArabicToEnglish(originalValue);
                if (originalValue !== convertedValue) {
                    node.setAttribute(attr, convertedValue);
                }
            }
        });
        
        // Process child nodes
        for (let i = 0; i < node.childNodes.length; i++) {
            processNode(node.childNodes[i]);
        }
    }
}

/**
 * Convert Arabic numerals in the entire document
 */
function convertDocumentNumbers() {
    processNode(document.body);
}

/**
 * Debounce function to limit the frequency of function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Debounced version of the converter function
const debouncedConverter = debounce(convertDocumentNumbers, 100);

/**
 * Initialize the number converter
 */
function initializeNumberConverter() {
    // Convert numbers on initial page load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', convertDocumentNumbers);
    } else {
        convertDocumentNumbers();
    }
    
    // Set up MutationObserver to handle dynamic content changes
    const observer = new MutationObserver((mutations) => {
        let shouldConvert = false;
        
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE || node.nodeType === Node.TEXT_NODE) {
                        shouldConvert = true;
                    }
                });
            } else if (mutation.type === 'characterData') {
                shouldConvert = true;
            }
        });
        
        if (shouldConvert) {
            debouncedConverter();
        }
    });
    
    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: true,
        attributeFilter: ['title', 'alt', 'data-original-title', 'placeholder', 'value']
    });
    
    // Handle AJAX requests and dynamic updates
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args).then((response) => {
            // Convert numbers after AJAX response
            setTimeout(debouncedConverter, 50);
            return response;
        });
    };
    
    // Handle jQuery AJAX if available
    if (window.$ && window.$.ajaxSetup) {
        window.$(document).ajaxComplete(() => {
            setTimeout(debouncedConverter, 50);
        });
    }
}

// Initialize the converter when DOM is ready
(function() {
    'use strict';

    // Make functions globally available
    if (typeof window !== 'undefined') {
        window.convertArabicToEnglish = convertArabicToEnglish;
        window.processNode = processNode;
        window.initializeNumberConverter = initializeNumberConverter;
    }

    // Initialize based on document state
    function init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeNumberConverter);
        } else {
            initializeNumberConverter();
        }
    }

    // Direct initialization for maximum compatibility
    init();
})();
