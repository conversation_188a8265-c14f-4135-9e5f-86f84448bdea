<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo Biometric Devices -->
    <record id="demo_device_main_entrance" model="biometric.device">
        <field name="name">Main Entrance Device</field>
        <field name="ip_address">*************</field>
        <field name="port">4370</field>
        <field name="device_type">zkteco</field>
        <field name="model">SpeedFace-V5L</field>
        <field name="serial_number">DEMO001</field>
        <field name="firmware_version">6.60</field>
        <field name="timeout">30</field>
        <field name="active" eval="True"/>
    </record>

    <record id="demo_device_back_entrance" model="biometric.device">
        <field name="name">Back Entrance Device</field>
        <field name="ip_address">*************</field>
        <field name="port">4370</field>
        <field name="device_type">zkteco</field>
        <field name="model">K40</field>
        <field name="serial_number">DEMO002</field>
        <field name="firmware_version">6.60</field>
        <field name="timeout">30</field>
        <field name="active" eval="True"/>
    </record>

    <!-- Demo Employees -->
    <record id="demo_employee_john" model="biometric.employee">
        <field name="name">John Smith</field>
        <field name="employee_code">EMP001</field>
        <field name="biometric_id">1001</field>
        <field name="card_number">C001</field>
        <field name="job_number">J001</field>
        <field name="email"><EMAIL></field>
        <field name="phone">+1234567890</field>
        <field name="mobile">+1234567891</field>
        <field name="department">IT Department</field>
        <field name="position">Software Developer</field>
        <field name="hire_date">2023-01-01</field>
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="active" eval="True"/>
        <field name="is_synced" eval="True"/>
        <field name="last_sync_date" eval="datetime.now()"/>
    </record>

    <record id="demo_employee_jane" model="biometric.employee">
        <field name="name">Jane Doe</field>
        <field name="employee_code">EMP002</field>
        <field name="biometric_id">1002</field>
        <field name="card_number">C002</field>
        <field name="job_number">J002</field>
        <field name="email"><EMAIL></field>
        <field name="phone">+1234567892</field>
        <field name="mobile">+1234567893</field>
        <field name="department">HR Department</field>
        <field name="position">HR Manager</field>
        <field name="hire_date">2022-08-01</field>
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="active" eval="True"/>
        <field name="is_synced" eval="True"/>
        <field name="last_sync_date" eval="datetime.now()"/>
    </record>

    <record id="demo_employee_mike" model="biometric.employee">
        <field name="name">Mike Johnson</field>
        <field name="employee_code">EMP003</field>
        <field name="biometric_id">1003</field>
        <field name="card_number">C003</field>
        <field name="job_number">J003</field>
        <field name="email"><EMAIL></field>
        <field name="phone">+1234567894</field>
        <field name="mobile">+1234567895</field>
        <field name="department">Security</field>
        <field name="position">Security Guard</field>
        <field name="hire_date">2023-05-01</field>
        <field name="schedule_id" ref="shift_work_schedule"/>
        <field name="active" eval="True"/>
        <field name="is_synced" eval="True"/>
        <field name="last_sync_date" eval="datetime.now()"/>
    </record>

    <record id="demo_employee_sarah" model="biometric.employee">
        <field name="name">Sarah Wilson</field>
        <field name="employee_code">EMP004</field>
        <field name="biometric_id">1004</field>
        <field name="card_number">C004</field>
        <field name="job_number">J004</field>
        <field name="email"><EMAIL></field>
        <field name="phone">+**********</field>
        <field name="mobile">+**********</field>
        <field name="department">Finance</field>
        <field name="position">Accountant</field>
        <field name="hire_date">2023-02-01</field>
        <field name="schedule_id" ref="default_work_schedule"/>
        <field name="active" eval="True"/>
        <field name="is_synced" eval="True"/>
        <field name="last_sync_date" eval="datetime.now()"/>
    </record>

    <!-- Demo Attendance Records for Today -->
    <record id="demo_attendance_john_checkin" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_john"/>
        <field name="attendance_date">2024-01-15</field>
        <field name="check_time">2024-01-15 08:15:00</field>
        <field name="attendance_type">check_in</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1001</field>
        <field name="verification_type">fingerprint</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <record id="demo_attendance_john_checkout" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_john"/>
        <field name="attendance_date">2024-01-15</field>
        <field name="check_time">2024-01-15 17:30:00</field>
        <field name="attendance_type">check_out</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1001</field>
        <field name="verification_type">fingerprint</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <record id="demo_attendance_jane_checkin" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_jane"/>
        <field name="attendance_date">2024-01-15</field>
        <field name="check_time">2024-01-15 07:45:00</field>
        <field name="attendance_type">check_in</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1002</field>
        <field name="verification_type">face</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <record id="demo_attendance_jane_checkout" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_jane"/>
        <field name="attendance_date">2024-01-15</field>
        <field name="check_time">2024-01-15 17:00:00</field>
        <field name="attendance_type">check_out</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1002</field>
        <field name="verification_type">face</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <record id="demo_attendance_mike_checkin" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_mike"/>
        <field name="attendance_date">2024-01-15</field>
        <field name="check_time">2024-01-15 06:00:00</field>
        <field name="attendance_type">check_in</field>
        <field name="device_id" ref="demo_device_back_entrance"/>
        <field name="device_user_id">1003</field>
        <field name="verification_type">card</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <!-- Demo Attendance Records for Yesterday -->
    <record id="demo_attendance_sarah_checkin_yesterday" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_sarah"/>
        <field name="attendance_date">2024-01-14</field>
        <field name="check_time">2024-01-14 08:30:00</field>
        <field name="attendance_type">check_in</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1004</field>
        <field name="verification_type">fingerprint</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <record id="demo_attendance_sarah_checkout_yesterday" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_sarah"/>
        <field name="attendance_date">2024-01-14</field>
        <field name="check_time">2024-01-14 16:45:00</field>
        <field name="attendance_type">check_out</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1004</field>
        <field name="verification_type">fingerprint</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <!-- Demo Late Attendance -->
    <record id="demo_attendance_john_late_yesterday" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_john"/>
        <field name="attendance_date">2024-01-14</field>
        <field name="check_time">2024-01-14 08:45:00</field>
        <field name="attendance_type">check_in</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1001</field>
        <field name="verification_type">fingerprint</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <record id="demo_attendance_john_checkout_yesterday" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_john"/>
        <field name="attendance_date">2024-01-14</field>
        <field name="check_time">2024-01-14 17:15:00</field>
        <field name="attendance_type">check_out</field>
        <field name="device_id" ref="demo_device_main_entrance"/>
        <field name="device_user_id">1001</field>
        <field name="verification_type">fingerprint</field>
        <field name="is_synced" eval="True"/>
        <field name="is_manual" eval="False"/>
    </record>

    <!-- Demo Manual Attendance Entry -->
    <record id="demo_attendance_jane_manual" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_jane"/>
        <field name="attendance_date">2024-01-13</field>
        <field name="check_time">2024-01-13 08:00:00</field>
        <field name="attendance_type">check_in</field>
        <field name="is_synced" eval="False"/>
        <field name="is_manual" eval="True"/>
        <field name="notes">Manual entry - device was offline</field>
    </record>

    <record id="demo_attendance_jane_manual_checkout" model="biometric.attendance">
        <field name="employee_id" ref="demo_employee_jane"/>
        <field name="attendance_date">2024-01-13</field>
        <field name="check_time">2024-01-13 17:00:00</field>
        <field name="attendance_type">check_out</field>
        <field name="is_synced" eval="False"/>
        <field name="is_manual" eval="True"/>
        <field name="notes">Manual entry - device was offline</field>
    </record>
</odoo>
