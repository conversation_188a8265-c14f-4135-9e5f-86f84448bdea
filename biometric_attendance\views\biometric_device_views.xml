<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Device Tree View -->
    <record id="view_biometric_device_tree" model="ir.ui.view">
        <field name="name">biometric.device.tree</field>
        <field name="model">biometric.device</field>
        <field name="arch" type="xml">
            <tree string="Biometric Devices" decoration-success="is_connected" decoration-danger="not is_connected">
                <field name="name"/>
                <field name="ip_address"/>
                <field name="port"/>
                <field name="device_type"/>
                <field name="model"/>
                <field name="is_connected" widget="boolean_toggle"/>
                <field name="last_sync_date"/>
                <field name="total_users"/>
                <field name="total_records"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Device Form View -->
    <record id="view_biometric_device_form" model="ir.ui.view">
        <field name="name">biometric.device.form</field>
        <field name="model">biometric.device</field>
        <field name="arch" type="xml">
            <form string="Biometric Device">
                <header>
                    <button name="test_connection" type="object" string="Test Connection" 
                            class="btn-primary"/>
                    <button name="sync_employees" type="object" string="Sync Employees" 
                            class="btn-secondary" attrs="{'invisible': [('is_connected', '=', False)]}"/>
                    <button name="sync_attendance" type="object" string="Sync Attendance" 
                            class="btn-secondary" attrs="{'invisible': [('is_connected', '=', False)]}"/>
                    <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_attendances" type="object" class="oe_stat_button" icon="fa-calendar">
                            <field name="total_records" widget="statinfo" string="Records"/>
                        </button>
                        <button name="sync_employees" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="total_users" widget="statinfo" string="Users"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Connected" bg_color="bg-success" 
                            attrs="{'invisible': [('is_connected', '=', False)]}"/>
                    <widget name="web_ribbon" title="Disconnected" bg_color="bg-danger" 
                            attrs="{'invisible': [('is_connected', '=', True)]}"/>
                    
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="name" required="1"/>
                            <field name="device_type" required="1"/>
                            <field name="model"/>
                        </group>
                        <group name="connection_info" string="Connection Settings">
                            <field name="ip_address" required="1"/>
                            <field name="port" required="1"/>
                            <field name="timeout"/>
                            <field name="password" password="True"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="device_info" string="Device Information">
                            <field name="serial_number" readonly="1"/>
                            <field name="firmware_version" readonly="1"/>
                        </group>
                        <group name="status_info" string="Status Information">
                            <field name="is_connected" readonly="1"/>
                            <field name="last_connection_test" readonly="1"/>
                            <field name="last_sync_date" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Attendance Records" name="attendance_records">
                            <field name="attendance_ids" readonly="1">
                                <tree>
                                    <field name="employee_id"/>
                                    <field name="attendance_date"/>
                                    <field name="check_time"/>
                                    <field name="attendance_type"/>
                                    <field name="verification_type"/>
                                    <field name="status"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Device Kanban View -->
    <record id="view_biometric_device_kanban" model="ir.ui.view">
        <field name="name">biometric.device.kanban</field>
        <field name="model">biometric.device</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="id"/>
                <field name="name"/>
                <field name="ip_address"/>
                <field name="port"/>
                <field name="device_type"/>
                <field name="model"/>
                <field name="is_connected"/>
                <field name="last_sync_date"/>
                <field name="total_users"/>
                <field name="total_records"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click #{record.active.raw_value ? '' : 'oe_kanban_color_2'}">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <small class="o_kanban_record_subtitle text-muted">
                                        <field name="ip_address"/>:<field name="port"/>
                                    </small>
                                </div>
                                <div class="o_kanban_manage_button_section">
                                    <a class="o_kanban_manage_toggle_button" href="#">
                                        <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                    </a>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span t-if="record.device_type.raw_value">
                                            <i class="fa fa-desktop"/> <field name="device_type"/>
                                        </span>
                                        <br/>
                                        <span t-if="record.model.raw_value">
                                            <i class="fa fa-tag"/> <field name="model"/>
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-if="record.is_connected.raw_value" class="badge badge-success">
                                            <i class="fa fa-check"/> Connected
                                        </span>
                                        <span t-if="!record.is_connected.raw_value" class="badge badge-danger">
                                            <i class="fa fa-times"/> Disconnected
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="row">
                                    <div class="col-6">
                                        <a name="sync_employees" type="object">
                                            <field name="total_users"/> Users
                                        </a>
                                    </div>
                                    <div class="col-6">
                                        <a name="action_view_attendances" type="object">
                                            <field name="total_records"/> Records
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom" t-if="record.last_sync_date.raw_value">
                                <small class="text-muted">
                                    Last sync: <field name="last_sync_date"/>
                                </small>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Device Search View -->
    <record id="view_biometric_device_search" model="ir.ui.view">
        <field name="name">biometric.device.search</field>
        <field name="model">biometric.device</field>
        <field name="arch" type="xml">
            <search string="Search Devices">
                <field name="name" string="Device" filter_domain="['|', '|', ('name', 'ilike', self), ('ip_address', 'ilike', self), ('model', 'ilike', self)]"/>
                <field name="device_type"/>
                <field name="ip_address"/>
                
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Connected" name="connected" domain="[('is_connected', '=', True)]"/>
                <filter string="Disconnected" name="disconnected" domain="[('is_connected', '=', False)]"/>
                <separator/>
                <filter string="ZKTeco" name="zkteco" domain="[('device_type', '=', 'zkteco')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Device Type" name="group_type" context="{'group_by': 'device_type'}"/>
                    <filter string="Connection Status" name="group_connection" context="{'group_by': 'is_connected'}"/>
                    <filter string="Model" name="group_model" context="{'group_by': 'model'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Device Action -->
    <record id="action_biometric_device" model="ir.actions.act_window">
        <field name="name">Biometric Devices</field>
        <field name="res_model">biometric.device</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add your first biometric device!
            </p>
            <p>
                Connect biometric devices to automatically sync employee data and attendance records.
                Make sure the device is connected to the network and accessible via TCP/IP.
            </p>
        </field>
    </record>
</odoo>
