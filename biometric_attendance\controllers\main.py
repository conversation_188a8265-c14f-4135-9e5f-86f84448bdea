# -*- coding: utf-8 -*-

from odoo import http, fields
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)


class BiometricAttendanceController(http.Controller):

    @http.route('/biometric/attendance/api/checkin', type='json', auth='user', methods=['POST'])
    def api_checkin(self, **kwargs):
        """API endpoint for manual check-in"""
        try:
            employee_id = kwargs.get('employee_id')
            if not employee_id:
                return {'success': False, 'message': 'Employee ID is required'}
            
            employee = request.env['biometric.employee'].browse(employee_id)
            if not employee.exists():
                return {'success': False, 'message': 'Employee not found'}
            
            # Create check-in record
            attendance = request.env['biometric.attendance'].create({
                'employee_id': employee.id,
                'attendance_date': request.env.context.get('tz_date', fields.Date.today()),
                'check_time': fields.Datetime.now(),
                'attendance_type': 'check_in',
                'is_manual': True,
                'is_synced': False,
                'notes': 'Manual check-in via API'
            })
            
            return {
                'success': True,
                'message': 'Check-in successful',
                'attendance_id': attendance.id
            }
            
        except Exception as e:
            _logger.error(f"API check-in error: {str(e)}")
            return {'success': False, 'message': str(e)}

    @http.route('/biometric/attendance/api/checkout', type='json', auth='user', methods=['POST'])
    def api_checkout(self, **kwargs):
        """API endpoint for manual check-out"""
        try:
            employee_id = kwargs.get('employee_id')
            if not employee_id:
                return {'success': False, 'message': 'Employee ID is required'}
            
            employee = request.env['biometric.employee'].browse(employee_id)
            if not employee.exists():
                return {'success': False, 'message': 'Employee not found'}
            
            # Create check-out record
            attendance = request.env['biometric.attendance'].create({
                'employee_id': employee.id,
                'attendance_date': request.env.context.get('tz_date', fields.Date.today()),
                'check_time': fields.Datetime.now(),
                'attendance_type': 'check_out',
                'is_manual': True,
                'is_synced': False,
                'notes': 'Manual check-out via API'
            })
            
            return {
                'success': True,
                'message': 'Check-out successful',
                'attendance_id': attendance.id
            }
            
        except Exception as e:
            _logger.error(f"API check-out error: {str(e)}")
            return {'success': False, 'message': str(e)}

    @http.route('/biometric/attendance/api/status', type='json', auth='user', methods=['GET'])
    def api_attendance_status(self, **kwargs):
        """API endpoint to get employee attendance status"""
        try:
            employee_id = kwargs.get('employee_id')
            if not employee_id:
                return {'success': False, 'message': 'Employee ID is required'}
            
            employee = request.env['biometric.employee'].browse(employee_id)
            if not employee.exists():
                return {'success': False, 'message': 'Employee not found'}
            
            # Get today's attendance
            today = request.env.context.get('tz_date', fields.Date.today())
            attendances = request.env['biometric.attendance'].search([
                ('employee_id', '=', employee.id),
                ('attendance_date', '=', today)
            ]).sorted('check_time')
            
            status = {
                'employee_id': employee.id,
                'employee_name': employee.name,
                'date': today.strftime('%Y-%m-%d'),
                'is_checked_in': False,
                'last_check_in': None,
                'last_check_out': None,
                'total_hours': 0,
                'status': 'absent'
            }
            
            if attendances:
                check_ins = attendances.filtered(lambda a: a.attendance_type == 'check_in')
                check_outs = attendances.filtered(lambda a: a.attendance_type == 'check_out')
                
                if check_ins:
                    last_check_in = check_ins[-1]
                    status['last_check_in'] = last_check_in.check_time.strftime('%H:%M')
                    status['status'] = last_check_in.status
                    
                    # Check if currently checked in
                    status['is_checked_in'] = len(check_ins) > len(check_outs)
                
                if check_outs:
                    last_check_out = check_outs[-1]
                    status['last_check_out'] = last_check_out.check_time.strftime('%H:%M')
                
                # Calculate total hours
                if check_ins and check_outs:
                    status['total_hours'] = check_ins[0].worked_hours
            
            return {'success': True, 'data': status}
            
        except Exception as e:
            _logger.error(f"API status error: {str(e)}")
            return {'success': False, 'message': str(e)}

    @http.route('/biometric/device/webhook', type='json', auth='none', methods=['POST'], csrf=False)
    def device_webhook(self, **kwargs):
        """Webhook endpoint for device data"""
        try:
            # This endpoint can be used by external devices to push attendance data
            data = request.jsonrequest
            
            if not data:
                return {'success': False, 'message': 'No data provided'}
            
            # Validate required fields
            required_fields = ['device_id', 'user_id', 'timestamp', 'type']
            for field in required_fields:
                if field not in data:
                    return {'success': False, 'message': f'Missing required field: {field}'}
            
            # Find device
            device = request.env['biometric.device'].sudo().search([
                ('serial_number', '=', data['device_id'])
            ], limit=1)
            
            if not device:
                return {'success': False, 'message': 'Device not found'}
            
            # Find employee
            employee = request.env['biometric.employee'].sudo().search([
                ('biometric_id', '=', str(data['user_id']))
            ], limit=1)
            
            if not employee:
                return {'success': False, 'message': 'Employee not found'}
            
            # Parse timestamp
            from datetime import datetime
            try:
                check_time = datetime.strptime(data['timestamp'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                return {'success': False, 'message': 'Invalid timestamp format'}
            
            # Create attendance record
            attendance = request.env['biometric.attendance'].sudo().create({
                'employee_id': employee.id,
                'attendance_date': check_time.date(),
                'check_time': check_time,
                'attendance_type': data['type'],
                'device_id': device.id,
                'device_user_id': str(data['user_id']),
                'verification_type': data.get('verification_type', 'fingerprint'),
                'is_synced': True,
                'notes': 'Created via webhook'
            })
            
            return {
                'success': True,
                'message': 'Attendance record created',
                'attendance_id': attendance.id
            }
            
        except Exception as e:
            _logger.error(f"Webhook error: {str(e)}")
            return {'success': False, 'message': 'Internal server error'}

    @http.route('/biometric/dashboard', type='http', auth='user', website=True)
    def attendance_dashboard(self, **kwargs):
        """Dashboard page for attendance overview"""
        try:
            # Get current user's employee record
            user = request.env.user
            employee = request.env['biometric.employee'].search([
                ('email', '=', user.email)
            ], limit=1)
            
            if not employee:
                return request.render('biometric_attendance.dashboard_no_employee')
            
            # Get today's attendance
            from datetime import date
            today = date.today()
            attendances = request.env['biometric.attendance'].search([
                ('employee_id', '=', employee.id),
                ('attendance_date', '=', today)
            ]).sorted('check_time')
            
            # Get this month's summary
            processor = request.env['biometric.attendance.processor']
            month_start = today.replace(day=1)
            summary = processor.generate_attendance_summary(
                employee.id, month_start, today
            )
            
            values = {
                'employee': employee,
                'attendances': attendances,
                'summary': summary,
                'today': today,
            }
            
            return request.render('biometric_attendance.dashboard', values)
            
        except Exception as e:
            _logger.error(f"Dashboard error: {str(e)}")
            return request.render('biometric_attendance.dashboard_error', {'error': str(e)})
