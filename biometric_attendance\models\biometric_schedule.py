# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class BiometricSchedule(models.Model):
    _name = 'biometric.schedule'
    _description = 'Work Schedule'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(string='Schedule Name', required=True, tracking=True)
    code = fields.Char(string='Schedule Code', required=True, tracking=True)
    description = fields.Text(string='Description')
    
    # Schedule Lines
    line_ids = fields.One2many('biometric.schedule.line', 'schedule_id', string='Schedule Lines')
    
    # Settings
    active = fields.Boolean(string='Active', default=True)
    grace_period_minutes = fields.Integer(string='Grace Period (Minutes)', default=15,
                                        help="Grace period for late arrival in minutes")
    overtime_threshold = fields.Float(string='Overtime Threshold (Hours)', default=8.0,
                                    help="Hours after which overtime is calculated")
    
    # Statistics
    employee_count = fields.Integer(string='Employees Count', compute='_compute_employee_count')
    
    @api.depends('line_ids')
    def _compute_employee_count(self):
        for schedule in self:
            schedule.employee_count = self.env['biometric.employee'].search_count([
                ('schedule_id', '=', schedule.id)
            ])
    
    @api.constrains('code')
    def _check_code_unique(self):
        for schedule in self:
            if schedule.code:
                existing = self.search([
                    ('code', '=', schedule.code),
                    ('id', '!=', schedule.id)
                ])
                if existing:
                    raise ValidationError(_('Schedule code must be unique!'))
    
    def action_view_employees(self):
        """View employees using this schedule"""
        self.ensure_one()
        return {
            'name': _('Employees'),
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.employee',
            'view_mode': 'tree,form',
            'domain': [('schedule_id', '=', self.id)],
            'context': {'default_schedule_id': self.id},
        }


class BiometricScheduleLine(models.Model):
    _name = 'biometric.schedule.line'
    _description = 'Schedule Line'
    _order = 'dayofweek, hour_from'

    schedule_id = fields.Many2one('biometric.schedule', string='Schedule', required=True, ondelete='cascade')
    
    dayofweek = fields.Selection([
        ('0', 'Monday'),
        ('1', 'Tuesday'),
        ('2', 'Wednesday'),
        ('3', 'Thursday'),
        ('4', 'Friday'),
        ('5', 'Saturday'),
        ('6', 'Sunday'),
    ], string='Day of Week', required=True)
    
    hour_from = fields.Float(string='Work From', required=True)
    hour_to = fields.Float(string='Work To', required=True)
    
    # Break Times
    break_hour_from = fields.Float(string='Break From')
    break_hour_to = fields.Float(string='Break To')
    
    # Settings
    is_working_day = fields.Boolean(string='Working Day', default=True)
    
    # Computed Fields
    working_hours = fields.Float(string='Working Hours', compute='_compute_working_hours', store=True)
    break_hours = fields.Float(string='Break Hours', compute='_compute_working_hours', store=True)
    net_working_hours = fields.Float(string='Net Working Hours', compute='_compute_working_hours', store=True)
    
    @api.depends('hour_from', 'hour_to', 'break_hour_from', 'break_hour_to', 'is_working_day')
    def _compute_working_hours(self):
        for line in self:
            if line.is_working_day and line.hour_from and line.hour_to:
                line.working_hours = line.hour_to - line.hour_from
                
                if line.break_hour_from and line.break_hour_to:
                    line.break_hours = line.break_hour_to - line.break_hour_from
                else:
                    line.break_hours = 0
                
                line.net_working_hours = line.working_hours - line.break_hours
            else:
                line.working_hours = 0
                line.break_hours = 0
                line.net_working_hours = 0
    
    @api.constrains('hour_from', 'hour_to')
    def _check_working_hours(self):
        for line in self:
            if line.hour_from >= line.hour_to:
                raise ValidationError(_('Work start time must be before end time!'))
    
    @api.constrains('break_hour_from', 'break_hour_to', 'hour_from', 'hour_to')
    def _check_break_hours(self):
        for line in self:
            if line.break_hour_from and line.break_hour_to:
                if line.break_hour_from >= line.break_hour_to:
                    raise ValidationError(_('Break start time must be before break end time!'))
                
                if line.break_hour_from < line.hour_from or line.break_hour_to > line.hour_to:
                    raise ValidationError(_('Break time must be within working hours!'))
    
    @api.constrains('schedule_id', 'dayofweek')
    def _check_unique_day(self):
        for line in self:
            existing = self.search([
                ('schedule_id', '=', line.schedule_id.id),
                ('dayofweek', '=', line.dayofweek),
                ('id', '!=', line.id)
            ])
            if existing:
                raise ValidationError(_('Only one schedule line per day is allowed!'))


class BiometricHoliday(models.Model):
    _name = 'biometric.holiday'
    _description = 'Holiday Calendar'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date desc'

    name = fields.Char(string='Holiday Name', required=True)
    date = fields.Date(string='Date', required=True, index=True)
    description = fields.Text(string='Description')
    
    # Types
    holiday_type = fields.Selection([
        ('public', 'Public Holiday'),
        ('religious', 'Religious Holiday'),
        ('national', 'National Holiday'),
        ('company', 'Company Holiday'),
    ], string='Type', default='public')
    
    # Settings
    active = fields.Boolean(string='Active', default=True)
    is_recurring = fields.Boolean(string='Recurring Annually', default=False)
    
    @api.constrains('date')
    def _check_unique_date(self):
        for holiday in self:
            existing = self.search([
                ('date', '=', holiday.date),
                ('id', '!=', holiday.id)
            ])
            if existing:
                raise ValidationError(_('Holiday date must be unique!'))
