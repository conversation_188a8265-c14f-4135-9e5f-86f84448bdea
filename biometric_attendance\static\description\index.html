<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biometric Attendance Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .feature h3 {
            margin: 0 0 15px 0;
            color: #667eea;
            font-size: 1.3em;
        }
        .feature ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature li {
            margin-bottom: 8px;
        }
        .screenshots {
            margin: 40px 0;
        }
        .screenshots h2 {
            text-align: center;
            color: #667eea;
            margin-bottom: 30px;
        }
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .screenshot {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .installation {
            background: #e9ecef;
            padding: 30px;
            border-radius: 8px;
            margin: 40px 0;
        }
        .installation h2 {
            color: #667eea;
            margin-top: 0;
        }
        .installation code {
            background: #fff;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .installation pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .footer {
            background: #2d3748;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Biometric Attendance Management</h1>
            <p>Complete biometric attendance system for Odoo 15</p>
        </div>
        
        <div class="content">
            <div class="features">
                <div class="feature">
                    <h3>🔌 Device Integration</h3>
                    <ul>
                        <li>Direct TCP/IP connection to ZKTeco devices</li>
                        <li>Real-time data synchronization</li>
                        <li>Support for fingerprint, face, and card verification</li>
                        <li>Automatic connection monitoring</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>👥 Employee Management</h3>
                    <ul>
                        <li>Independent employee records</li>
                        <li>Biometric ID and card management</li>
                        <li>Department and position tracking</li>
                        <li>Photo and template storage</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>⏰ Attendance Tracking</h3>
                    <ul>
                        <li>Automatic check-in/check-out recording</li>
                        <li>Real-time status calculation</li>
                        <li>Working hours and overtime tracking</li>
                        <li>Manual entry support</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>📅 Work Schedules</h3>
                    <ul>
                        <li>Flexible schedule configuration</li>
                        <li>Multiple shift support</li>
                        <li>Break time management</li>
                        <li>Holiday calendar integration</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>📊 Comprehensive Reports</h3>
                    <ul>
                        <li>Daily, weekly, monthly reports</li>
                        <li>Employee attendance summaries</li>
                        <li>Excel and PDF export</li>
                        <li>Customizable filters</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>🔧 Administration</h3>
                    <ul>
                        <li>Multi-level user permissions</li>
                        <li>Automated data processing</li>
                        <li>Scheduled synchronization</li>
                        <li>System configuration</li>
                    </ul>
                </div>
            </div>
            
            <div class="installation">
                <h2>📦 Installation</h2>
                <p>1. Download the module to your Odoo addons directory:</p>
                <pre>git clone &lt;repository-url&gt; /path/to/odoo/addons/biometric_attendance</pre>
                
                <p>2. Update the apps list in Odoo and install the module</p>
                
                <p>3. Configure your biometric devices and work schedules</p>
                
                <p>4. Add employees and start tracking attendance!</p>
            </div>
            
            <div class="screenshots">
                <h2>📸 Screenshots</h2>
                <div class="screenshot-grid">
                    <div class="screenshot">
                        <h4>Employee Management</h4>
                        <p>Manage employees with biometric data</p>
                    </div>
                    <div class="screenshot">
                        <h4>Attendance Dashboard</h4>
                        <p>Real-time attendance monitoring</p>
                    </div>
                    <div class="screenshot">
                        <h4>Device Management</h4>
                        <p>Configure and monitor biometric devices</p>
                    </div>
                    <div class="screenshot">
                        <h4>Reports & Analytics</h4>
                        <p>Comprehensive attendance reports</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>
                <strong>Biometric Attendance Management</strong> - 
                Professional attendance tracking for Odoo 15
            </p>
            <p>
                <a href="mailto:<EMAIL>">Support</a> | 
                <a href="https://docs.yourcompany.com">Documentation</a> | 
                <a href="https://github.com/yourcompany/biometric-attendance">GitHub</a>
            </p>
        </div>
    </div>
</body>
</html>
