<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Schedule Tree View -->
    <record id="view_biometric_schedule_tree" model="ir.ui.view">
        <field name="name">biometric.schedule.tree</field>
        <field name="model">biometric.schedule</field>
        <field name="arch" type="xml">
            <tree string="Work Schedules" decoration-muted="not active">
                <field name="name"/>
                <field name="code"/>
                <field name="employee_count"/>
                <field name="grace_period_minutes"/>
                <field name="overtime_threshold"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Schedule Form View -->
    <record id="view_biometric_schedule_form" model="ir.ui.view">
        <field name="name">biometric.schedule.form</field>
        <field name="model">biometric.schedule</field>
        <field name="arch" type="xml">
            <form string="Work Schedule">
                <header>
                    <button name="action_view_employees" type="object" string="View Employees" 
                            class="btn-secondary" attrs="{'invisible': [('employee_count', '=', 0)]}"/>
                    <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_employees" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="employee_count" widget="statinfo" string="Employees"/>
                        </button>
                    </div>
                    
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="name" required="1"/>
                            <field name="code" required="1"/>
                            <field name="description"/>
                        </group>
                        <group name="settings" string="Settings">
                            <field name="grace_period_minutes"/>
                            <field name="overtime_threshold"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Schedule Lines" name="schedule_lines">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="dayofweek"/>
                                    <field name="is_working_day"/>
                                    <field name="hour_from" widget="float_time" attrs="{'required': [('is_working_day', '=', True)]}"/>
                                    <field name="hour_to" widget="float_time" attrs="{'required': [('is_working_day', '=', True)]}"/>
                                    <field name="break_hour_from" widget="float_time"/>
                                    <field name="break_hour_to" widget="float_time"/>
                                    <field name="working_hours" widget="float_time"/>
                                    <field name="break_hours" widget="float_time"/>
                                    <field name="net_working_hours" widget="float_time"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Schedule Search View -->
    <record id="view_biometric_schedule_search" model="ir.ui.view">
        <field name="name">biometric.schedule.search</field>
        <field name="model">biometric.schedule</field>
        <field name="arch" type="xml">
            <search string="Search Schedules">
                <field name="name" string="Schedule" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="With Employees" name="with_employees" domain="[('employee_count', '>', 0)]"/>
                <filter string="Without Employees" name="without_employees" domain="[('employee_count', '=', 0)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Grace Period" name="group_grace" context="{'group_by': 'grace_period_minutes'}"/>
                    <filter string="Overtime Threshold" name="group_overtime" context="{'group_by': 'overtime_threshold'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Schedule Action -->
    <record id="action_biometric_schedule" model="ir.actions.act_window">
        <field name="name">Work Schedules</field>
        <field name="res_model">biometric.schedule</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first work schedule!
            </p>
            <p>
                Work schedules define working hours, break times, and attendance rules for employees.
                You can create different schedules for different departments or shifts.
            </p>
        </field>
    </record>

    <!-- Holiday Tree View -->
    <record id="view_biometric_holiday_tree" model="ir.ui.view">
        <field name="name">biometric.holiday.tree</field>
        <field name="model">biometric.holiday</field>
        <field name="arch" type="xml">
            <tree string="Holidays" decoration-muted="not active">
                <field name="name"/>
                <field name="date"/>
                <field name="holiday_type"/>
                <field name="is_recurring"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Holiday Form View -->
    <record id="view_biometric_holiday_form" model="ir.ui.view">
        <field name="name">biometric.holiday.form</field>
        <field name="model">biometric.holiday</field>
        <field name="arch" type="xml">
            <form string="Holiday">
                <header>
                    <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                </header>
                <sheet>
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="name" required="1"/>
                            <field name="date" required="1"/>
                            <field name="holiday_type"/>
                        </group>
                        <group name="settings" string="Settings">
                            <field name="is_recurring"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description"/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Holiday Calendar View -->
    <record id="view_biometric_holiday_calendar" model="ir.ui.view">
        <field name="name">biometric.holiday.calendar</field>
        <field name="model">biometric.holiday</field>
        <field name="arch" type="xml">
            <calendar string="Holiday Calendar" date_start="date" color="holiday_type" mode="month">
                <field name="name"/>
                <field name="holiday_type"/>
            </calendar>
        </field>
    </record>

    <!-- Holiday Search View -->
    <record id="view_biometric_holiday_search" model="ir.ui.view">
        <field name="name">biometric.holiday.search</field>
        <field name="model">biometric.holiday</field>
        <field name="arch" type="xml">
            <search string="Search Holidays">
                <field name="name"/>
                <field name="date"/>
                <field name="holiday_type"/>
                
                <filter string="This Year" name="this_year" domain="[]"/>
                <filter string="Next Year" name="next_year" domain="[]"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Recurring" name="recurring" domain="[('is_recurring', '=', True)]"/>
                <filter string="One Time" name="one_time" domain="[('is_recurring', '=', False)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Type" name="group_type" context="{'group_by': 'holiday_type'}"/>
                    <filter string="Month" name="group_month" context="{'group_by': 'date:month'}"/>
                    <filter string="Year" name="group_year" context="{'group_by': 'date:year'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Holiday Action -->
    <record id="action_biometric_holiday" model="ir.actions.act_window">
        <field name="name">Holidays</field>
        <field name="res_model">biometric.holiday</field>
        <field name="view_mode">tree,form,calendar</field>
        <field name="context">{'search_default_this_year': 1, 'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first holiday!
            </p>
            <p>
                Define public holidays and non-working days to exclude them from attendance calculations.
                You can set holidays as recurring annually or one-time events.
            </p>
        </field>
    </record>
</odoo>
