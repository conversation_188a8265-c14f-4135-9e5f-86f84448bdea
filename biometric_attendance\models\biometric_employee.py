# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re


class BiometricEmployee(models.Model):
    _name = 'biometric.employee'
    _description = 'Biometric Employee'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'employee_code, name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Employee Name', required=True, index=True, tracking=True)
    employee_code = fields.Char(string='Employee Code', required=True, index=True, copy=False, tracking=True)
    card_number = fields.Char(string='Card Number', index=True, tracking=True)
    biometric_id = fields.Char(string='Biometric ID', required=True, index=True, copy=False, tracking=True)
    job_number = fields.Char(string='Job Number', index=True)
    
    # Contact Information
    email = fields.Char(string='Email')
    phone = fields.Char(string='Phone')
    mobile = fields.Char(string='Mobile')
    
    # Employment Information
    department = fields.Char(string='Department')
    position = fields.Char(string='Position')
    hire_date = fields.Date(string='Hire Date')
    
    # Biometric Information
    fingerprint_template = fields.Text(string='Fingerprint Template')
    face_template = fields.Text(string='Face Template')
    
    # Schedule Information
    schedule_id = fields.Many2one('biometric.schedule', string='Work Schedule')
    
    # Status
    active = fields.Boolean(string='Active', default=True)
    is_synced = fields.Boolean(string='Synced with Device', default=False)
    last_sync_date = fields.Datetime(string='Last Sync Date')
    
    # Attendance Statistics
    total_attendances = fields.Integer(string='Total Attendances', compute='_compute_attendance_stats')
    total_delays = fields.Integer(string='Total Delays', compute='_compute_attendance_stats')
    total_absences = fields.Integer(string='Total Absences', compute='_compute_attendance_stats')
    
    # Relations
    attendance_ids = fields.One2many('biometric.attendance', 'employee_id', string='Attendances')
    
    @api.depends('attendance_ids')
    def _compute_attendance_stats(self):
        for employee in self:
            attendances = employee.attendance_ids
            employee.total_attendances = len(attendances)
            employee.total_delays = len(attendances.filtered('is_late'))
            employee.total_absences = len(attendances.filtered(lambda a: a.status == 'absent'))
    
    @api.constrains('employee_code')
    def _check_employee_code(self):
        for employee in self:
            if employee.employee_code:
                existing = self.search([
                    ('employee_code', '=', employee.employee_code),
                    ('id', '!=', employee.id)
                ])
                if existing:
                    raise ValidationError(_('Employee code must be unique!'))
    
    @api.constrains('biometric_id')
    def _check_biometric_id(self):
        for employee in self:
            if employee.biometric_id:
                existing = self.search([
                    ('biometric_id', '=', employee.biometric_id),
                    ('id', '!=', employee.id)
                ])
                if existing:
                    raise ValidationError(_('Biometric ID must be unique!'))
    
    @api.constrains('email')
    def _check_email(self):
        for employee in self:
            if employee.email:
                if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', employee.email):
                    raise ValidationError(_('Please enter a valid email address!'))
    
    def name_get(self):
        result = []
        for employee in self:
            name = f"[{employee.employee_code}] {employee.name}"
            result.append((employee.id, name))
        return result
    
    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        if name:
            employees = self.search([
                '|', '|',
                ('name', operator, name),
                ('employee_code', operator, name),
                ('biometric_id', operator, name)
            ] + args, limit=limit)
            return employees.name_get()
        return super().name_search(name, args, operator, limit)
    
    def action_sync_with_device(self):
        """Sync employee data with biometric device"""
        # This will be implemented in the device integration
        self.ensure_one()
        # TODO: Implement device sync logic
        self.write({
            'is_synced': True,
            'last_sync_date': fields.Datetime.now()
        })
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Employee synced with device successfully!'),
                'type': 'success',
            }
        }
    
    def action_view_attendances(self):
        """View employee attendances"""
        self.ensure_one()
        return {
            'name': _('Attendances'),
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.attendance',
            'view_mode': 'tree,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {'default_employee_id': self.id},
        }
