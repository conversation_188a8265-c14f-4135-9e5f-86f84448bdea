# -*- coding: utf-8 -*-
{
    'name': 'Biometric Attendance Management',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'Complete biometric attendance system with ZKTeco device integration',
    'description': """
Biometric Attendance Management System
=====================================

This module provides a complete biometric attendance management system that is fully independent 
from Odoo's standard HR modules. It includes:

Features:
---------
* Direct integration with ZKTeco biometric devices via TCP/IP
* Employee management with biometric data
* Attendance tracking with check-in/check-out
* Working time schedules and rules
* Automatic calculation of delays and absences
* Comprehensive reporting system
* Real-time data synchronization
* Multi-level user permissions

Device Support:
--------------
* ZKTeco devices (SpeedFace-V5L, etc.)
* TCP/IP communication protocol
* Automatic data fetching via scheduled tasks

Reports:
--------
* Daily, weekly, and monthly attendance reports
* Working hours calculation
* Delay and absence tracking
* Employee attendance summary

Security:
---------
* Biometric Administrator
* Attendance Supervisor  
* View Only access

Technical:
----------
* Independent from hr.employee and hr.attendance
* Custom models for complete control
* Scheduled tasks for automatic synchronization
* Professional MVC structure
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web'],
    'data': [
        # Security
        'security/biometric_security.xml',
        'security/ir.model.access.csv',
        
        # Data
        'data/biometric_data.xml',
        'data/biometric_cron.xml',
        
        # Views
        'views/biometric_employee_views.xml',
        'views/biometric_attendance_views.xml',
        'views/biometric_schedule_views.xml',
        'views/biometric_device_views.xml',
        'views/biometric_report_views.xml',
        'views/biometric_menus.xml',
        
        # Reports
        'reports/biometric_reports.xml',
        'reports/attendance_report_template.xml',
    ],
    'demo': [
        'demo/biometric_demo.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'biometric_attendance/static/src/css/biometric_attendance.css',
            'biometric_attendance/static/src/js/biometric_attendance.js',
        ],
    },
    'images': ['static/description/banner.png'],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    'external_dependencies': {
        'python': ['socket', 'struct', 'datetime'],
    },
}
