# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, date, timedelta


class BiometricSyncWizard(models.TransientModel):
    _name = 'biometric.sync.wizard'
    _description = 'Device Sync Wizard'

    device_id = fields.Many2one('biometric.device', string='Device', required=True)
    sync_type = fields.Selection([
        ('employees', 'Sync Employees'),
        ('attendance', 'Sync Attendance'),
        ('both', 'Sync Both'),
    ], string='Sync Type', required=True, default='both')
    
    # Attendance sync options
    date_from = fields.Date(string='Date From', default=lambda self: date.today() - timedelta(days=7))
    date_to = fields.Date(string='Date To', default=fields.Date.today)
    
    # Options
    overwrite_existing = fields.Boolean(string='Overwrite Existing Data', default=False)
    create_missing_employees = fields.Boolean(string='Create Missing Employees', default=True)
    
    # Results
    sync_results = fields.Text(string='Sync Results', readonly=True)
    
    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for wizard in self:
            if wizard.date_from > wizard.date_to:
                raise ValidationError(_('Date From must be before Date To!'))
    
    def action_sync(self):
        """Perform the sync operation"""
        self.ensure_one()
        
        if not self.device_id.is_connected:
            # Test connection first
            try:
                self.device_id.test_connection()
            except UserError:
                raise UserError(_('Cannot connect to device. Please check device connection.'))
        
        results = []
        
        try:
            if self.sync_type in ['employees', 'both']:
                employee_result = self._sync_employees()
                results.append(employee_result)
            
            if self.sync_type in ['attendance', 'both']:
                attendance_result = self._sync_attendance()
                results.append(attendance_result)
            
            self.sync_results = '\n'.join(results)
            
            return {
                'name': _('Sync Results'),
                'type': 'ir.actions.act_window',
                'res_model': self._name,
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': {'show_results': True},
            }
            
        except Exception as e:
            raise UserError(_('Sync failed: %s') % str(e))
    
    def _sync_employees(self):
        """Sync employees from device"""
        try:
            employees_data = self.device_id._get_employees_from_device()
            
            created_count = 0
            updated_count = 0
            skipped_count = 0
            
            for emp_data in employees_data:
                existing_employee = self.env['biometric.employee'].search([
                    ('biometric_id', '=', emp_data['biometric_id'])
                ], limit=1)
                
                if existing_employee:
                    if self.overwrite_existing:
                        existing_employee.write({
                            'name': emp_data.get('name', existing_employee.name),
                            'card_number': emp_data.get('card_number'),
                            'is_synced': True,
                            'last_sync_date': fields.Datetime.now()
                        })
                        updated_count += 1
                    else:
                        skipped_count += 1
                else:
                    if self.create_missing_employees:
                        # Get default schedule
                        default_schedule = self.env['biometric.schedule'].search([
                            ('code', '=', 'DEFAULT')
                        ], limit=1)
                        
                        self.env['biometric.employee'].create({
                            'name': emp_data['name'],
                            'biometric_id': emp_data['biometric_id'],
                            'card_number': emp_data.get('card_number'),
                            'employee_code': emp_data.get('employee_code', emp_data['biometric_id']),
                            'schedule_id': default_schedule.id if default_schedule else False,
                            'is_synced': True,
                            'last_sync_date': fields.Datetime.now()
                        })
                        created_count += 1
                    else:
                        skipped_count += 1
            
            result = f"Employee Sync Results:\n"
            result += f"- Created: {created_count}\n"
            result += f"- Updated: {updated_count}\n"
            result += f"- Skipped: {skipped_count}\n"
            
            return result
            
        except Exception as e:
            return f"Employee Sync Failed: {str(e)}"
    
    def _sync_attendance(self):
        """Sync attendance from device"""
        try:
            attendance_data = self.device_id._get_attendance_from_device(
                self.date_from, self.date_to
            )
            
            created_count = 0
            skipped_count = 0
            error_count = 0
            
            for att_data in attendance_data:
                try:
                    # Check if record already exists
                    existing = self.env['biometric.attendance'].search([
                        ('device_id', '=', self.device_id.id),
                        ('device_user_id', '=', att_data['user_id']),
                        ('check_time', '=', att_data['check_time'])
                    ])
                    
                    if existing and not self.overwrite_existing:
                        skipped_count += 1
                        continue
                    
                    # Find employee by biometric_id
                    employee = self.env['biometric.employee'].search([
                        ('biometric_id', '=', att_data['user_id'])
                    ], limit=1)
                    
                    if not employee:
                        if self.create_missing_employees:
                            # Create employee
                            employee = self.env['biometric.employee'].create({
                                'name': f"Employee {att_data['user_id']}",
                                'biometric_id': att_data['user_id'],
                                'employee_code': att_data['user_id'],
                                'is_synced': True,
                                'last_sync_date': fields.Datetime.now()
                            })
                        else:
                            error_count += 1
                            continue
                    
                    if existing:
                        # Update existing record
                        existing.write({
                            'employee_id': employee.id,
                            'attendance_date': att_data['check_time'].date(),
                            'check_time': att_data['check_time'],
                            'attendance_type': att_data.get('type', 'check_in'),
                            'device_id': self.device_id.id,
                            'device_user_id': att_data['user_id'],
                            'verification_type': att_data.get('verification_type', 'fingerprint'),
                            'is_synced': True,
                        })
                    else:
                        # Create new record
                        self.env['biometric.attendance'].create({
                            'employee_id': employee.id,
                            'attendance_date': att_data['check_time'].date(),
                            'check_time': att_data['check_time'],
                            'attendance_type': att_data.get('type', 'check_in'),
                            'device_id': self.device_id.id,
                            'device_user_id': att_data['user_id'],
                            'verification_type': att_data.get('verification_type', 'fingerprint'),
                            'is_synced': True,
                        })
                        created_count += 1
                
                except Exception as e:
                    error_count += 1
                    continue
            
            result = f"Attendance Sync Results:\n"
            result += f"- Created: {created_count}\n"
            result += f"- Skipped: {skipped_count}\n"
            result += f"- Errors: {error_count}\n"
            
            return result
            
        except Exception as e:
            return f"Attendance Sync Failed: {str(e)}"
    
    def action_close(self):
        """Close the wizard"""
        return {'type': 'ir.actions.act_window_close'}


class BiometricProcessAttendanceWizard(models.TransientModel):
    _name = 'biometric.process.attendance.wizard'
    _description = 'Process Attendance Wizard'

    date_from = fields.Date(string='Date From', required=True, default=fields.Date.today)
    date_to = fields.Date(string='Date To', required=True, default=fields.Date.today)
    employee_ids = fields.Many2many('biometric.employee', string='Employees')
    
    # Options
    recalculate_existing = fields.Boolean(string='Recalculate Existing Records', default=False)
    create_absence_records = fields.Boolean(string='Create Absence Records', default=True)
    
    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for wizard in self:
            if wizard.date_from > wizard.date_to:
                raise ValidationError(_('Date From must be before Date To!'))
    
    def action_process(self):
        """Process attendance records"""
        self.ensure_one()
        
        processor = self.env['biometric.attendance.processor']
        
        # Get employees to process
        employees = self.employee_ids
        if not employees:
            employees = self.env['biometric.employee'].search([('active', '=', True)])
        
        processed_count = 0
        current_date = self.date_from
        
        while current_date <= self.date_to:
            for employee in employees:
                try:
                    processor._process_employee_attendance(employee, current_date)
                    processed_count += 1
                except Exception as e:
                    continue
            
            current_date += timedelta(days=1)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Processing Complete'),
                'message': _('Processed %d employee-day records') % processed_count,
                'type': 'success',
            }
        }
