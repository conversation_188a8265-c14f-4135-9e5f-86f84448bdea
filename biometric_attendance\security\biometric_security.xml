<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Categories -->
    <record id="module_category_biometric_attendance" model="ir.module.category">
        <field name="name">Biometric Attendance</field>
        <field name="description">Manage biometric attendance system permissions</field>
        <field name="sequence">10</field>
    </record>

    <!-- User Groups -->
    
    <!-- Biometric User (View Only) -->
    <record id="group_biometric_user" model="res.groups">
        <field name="name">Biometric User</field>
        <field name="category_id" ref="module_category_biometric_attendance"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="comment">View only access to biometric attendance data</field>
    </record>

    <!-- Attendance Supervisor -->
    <record id="group_biometric_supervisor" model="res.groups">
        <field name="name">Attendance Supervisor</field>
        <field name="category_id" ref="module_category_biometric_attendance"/>
        <field name="implied_ids" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="comment">Can manage attendance records and generate reports</field>
    </record>

    <!-- Biometric Administrator -->
    <record id="group_biometric_admin" model="res.groups">
        <field name="name">Biometric Administrator</field>
        <field name="category_id" ref="module_category_biometric_attendance"/>
        <field name="implied_ids" eval="[(4, ref('group_biometric_supervisor'))]"/>
        <field name="comment">Full access to biometric attendance system including device management</field>
    </record>

    <!-- Record Rules -->
    
    <!-- Employee Access Rules -->
    <record id="biometric_employee_rule_user" model="ir.rule">
        <field name="name">Biometric Employee: User Access</field>
        <field name="model_id" ref="model_biometric_employee"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_employee_rule_supervisor" model="ir.rule">
        <field name="name">Biometric Employee: Supervisor Access</field>
        <field name="model_id" ref="model_biometric_employee"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_supervisor'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_employee_rule_admin" model="ir.rule">
        <field name="name">Biometric Employee: Admin Access</field>
        <field name="model_id" ref="model_biometric_employee"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Attendance Access Rules -->
    <record id="biometric_attendance_rule_user" model="ir.rule">
        <field name="name">Biometric Attendance: User Access</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_attendance_rule_supervisor" model="ir.rule">
        <field name="name">Biometric Attendance: Supervisor Access</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_supervisor'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_attendance_rule_admin" model="ir.rule">
        <field name="name">Biometric Attendance: Admin Access</field>
        <field name="model_id" ref="model_biometric_attendance"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Device Access Rules -->
    <record id="biometric_device_rule_user" model="ir.rule">
        <field name="name">Biometric Device: User Access</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_device_rule_supervisor" model="ir.rule">
        <field name="name">Biometric Device: Supervisor Access</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_supervisor'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_device_rule_admin" model="ir.rule">
        <field name="name">Biometric Device: Admin Access</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Schedule Access Rules -->
    <record id="biometric_schedule_rule_user" model="ir.rule">
        <field name="name">Biometric Schedule: User Access</field>
        <field name="model_id" ref="model_biometric_schedule"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_schedule_rule_supervisor" model="ir.rule">
        <field name="name">Biometric Schedule: Supervisor Access</field>
        <field name="model_id" ref="model_biometric_schedule"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_supervisor'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_schedule_rule_admin" model="ir.rule">
        <field name="name">Biometric Schedule: Admin Access</field>
        <field name="model_id" ref="model_biometric_schedule"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Holiday Access Rules -->
    <record id="biometric_holiday_rule_user" model="ir.rule">
        <field name="name">Biometric Holiday: User Access</field>
        <field name="model_id" ref="model_biometric_holiday"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_holiday_rule_supervisor" model="ir.rule">
        <field name="name">Biometric Holiday: Supervisor Access</field>
        <field name="model_id" ref="model_biometric_holiday"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_supervisor'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="biometric_holiday_rule_admin" model="ir.rule">
        <field name="name">Biometric Holiday: Admin Access</field>
        <field name="model_id" ref="model_biometric_holiday"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Settings Access Rules -->
    <record id="biometric_settings_rule_admin" model="ir.rule">
        <field name="name">Biometric Settings: Admin Only</field>
        <field name="model_id" ref="model_biometric_settings"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Processor Access Rules -->
    <record id="biometric_processor_rule_admin" model="ir.rule">
        <field name="name">Biometric Processor: Admin Only</field>
        <field name="model_id" ref="model_biometric_attendance_processor"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_biometric_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
</odoo>
